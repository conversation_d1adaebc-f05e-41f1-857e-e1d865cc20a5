{"__meta": {"id": "01K2Y1VJEQHRVZMS63YMB2EGYX", "datetime": "2025-08-18 08:38:45", "utime": **********.592295, "method": "GET", "uri": "/cache/original/product/240/jzFdRJ2EX7yX7AoXVaoS280l5WpIimohL5cVXQhu.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.40477, "end": **********.601084, "duration": 0.19631409645080566, "duration_str": "196ms", "measures": [{"label": "Booting", "start": **********.40477, "relative_start": 0, "end": **********.575527, "relative_end": **********.575527, "duration": 0.*****************, "duration_str": "171ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.575538, "relative_start": 0.*****************, "end": **********.601087, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "25.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.584903, "relative_start": 0.*****************, "end": **********.588251, "relative_end": **********.588251, "duration": 0.003348112106323242, "duration_str": "3.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.590976, "relative_start": 0.*****************, "end": **********.591063, "relative_end": **********.591063, "duration": 8.702278137207031e-05, "duration_str": "87μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.591076, "relative_start": 0.*****************, "end": **********.591087, "relative_end": **********.591087, "duration": 1.1205673217773438e-05, "duration_str": "11μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/240/jzFdRJ2EX7yX7AoXVaoS280l5WpIimohL5cVXQhu.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "197ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1682228307 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1682228307\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1165896250 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1165896250\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-460086868 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IkhiUkxJUHVJS0NKZ0tDOGt4bjVLc3c9PSIsInZhbHVlIjoiRDZQbmJGLzRxbFREVDN4eFZ4NWhxcEoxaHJodExJSTJlN0pObzNWUmZsZE03M2QrOWR2OEhvZGhveXJueHd2UnJ1WmF6cHRlai8vVnFUWi8xM05LaEhLbk9TZ0dYaldsQXNlZVNaWWNrNFY3cEg4ZlYyRTNOYjBTeHcyQWlFSy8iLCJtYWMiOiIzMzM5ZTY5NjNmYmYyM2JlMzEwMDc0NWI3ZmI1MDQwNTQxOWNlNzk3NDg4ODc4OTdlNmE5MjUyNmVhZjcxYWQ3IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Ikx1TVZzS2FsdlVjQzdUUnBPQ3F5akE9PSIsInZhbHVlIjoiMDJpWU5USjJBdEZSbEo4a0Q3NitFZzU5ZEZIeUVXSnhRWHpzUW9FZFVGek1vQVhQTFIxVGNqUW1Tak5lRUhFLzNROG9wQnNMZjZnQlkzbnhUZGtsY0J1SHdrYkFzVWVZUi8yUDNIa1IwYVp5b1BHZlIvU0pwZFpuN1lDcHpPOUwiLCJtYWMiOiIzNDViZTliZWUyZTg2OWExNDM2MjU5OGEzZGMyN2QzNGUxYTIzMzc4YjZmZGFjNjVjODU0ZTAxMzgwOWVmM2NmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-C<PERSON>,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://mlk.test/5641234-variant-3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-460086868\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2117558768 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkhiUkxJUHVJS0NKZ0tDOGt4bjVLc3c9PSIsInZhbHVlIjoiRDZQbmJGLzRxbFREVDN4eFZ4NWhxcEoxaHJodExJSTJlN0pObzNWUmZsZE03M2QrOWR2OEhvZGhveXJueHd2UnJ1WmF6cHRlai8vVnFUWi8xM05LaEhLbk9TZ0dYaldsQXNlZVNaWWNrNFY3cEg4ZlYyRTNOYjBTeHcyQWlFSy8iLCJtYWMiOiIzMzM5ZTY5NjNmYmYyM2JlMzEwMDc0NWI3ZmI1MDQwNTQxOWNlNzk3NDg4ODc4OTdlNmE5MjUyNmVhZjcxYWQ3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ikx1TVZzS2FsdlVjQzdUUnBPQ3F5akE9PSIsInZhbHVlIjoiMDJpWU5USjJBdEZSbEo4a0Q3NitFZzU5ZEZIeUVXSnhRWHpzUW9FZFVGek1vQVhQTFIxVGNqUW1Tak5lRUhFLzNROG9wQnNMZjZnQlkzbnhUZGtsY0J1SHdrYkFzVWVZUi8yUDNIa1IwYVp5b1BHZlIvU0pwZFpuN1lDcHpPOUwiLCJtYWMiOiIzNDViZTliZWUyZTg2OWExNDM2MjU5OGEzZGMyN2QzNGUxYTIzMzc4YjZmZGFjNjVjODU0ZTAxMzgwOWVmM2NmIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117558768\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">60398</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">8546f01c65af44f66cdb8cafb4bc4dc9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:38:45 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1980447265 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1980447265\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/240/jzFdRJ2EX7yX7AoXVaoS280l5WpIimohL5cVXQhu.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}