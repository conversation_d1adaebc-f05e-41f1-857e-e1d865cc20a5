{"__meta": {"id": "01K2Y2BZCZTCB3N69STK7X6GYB", "datetime": "2025-08-18 08:47:43", "utime": **********.135962, "method": "GET", "uri": "/iphone-ip1212-pro", "ip": "127.0.0.1"}, "modules": {"count": 7, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (73)", "Webkul\\Attribute\\Models\\AttributeFamily (5)", "Webkul\\Attribute\\Models\\AttributeTranslation (35)", "Webkul\\Attribute\\Models\\AttributeOption (12)", "Webkul\\Attribute\\Models\\AttributeOptionTranslation (60)"], "views": [], "queries": [{"sql": "select * from `attributes` where `code` = 'url_key'", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 1.69, "duration_str": "1.69s", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "duration": 1.58, "duration_str": "1.58s", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2 and `attributes`.`is_visible_on_front` = 1", "duration": 0.31, "duration_str": "310ms", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 23 and `attribute_translations`.`attribute_id` is not null", "duration": 2.18, "duration_str": "2.18s", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`id` = 371 limit 1", "duration": 1.73, "duration_str": "1.73s", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 371 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 1.66, "duration_str": "1.66s", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 25 and `attribute_translations`.`attribute_id` is not null", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `id` in ('395')", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 395 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 33 and `attribute_translations`.`attribute_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 38 and `attribute_translations`.`attribute_id` is not null", "duration": 0.36, "duration_str": "360ms", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 39 and `attribute_translations`.`attribute_id` is not null", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 9 limit 1", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 38 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 1 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 25 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 27 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 30 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 33 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 34 limit 1", "duration": 0.36, "duration_str": "360ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 39 limit 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 28 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 16 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 17 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 18 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 4 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 5 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 6 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 7 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 8 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 26 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`id` = 371 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.34, "duration_str": "340ms", "connection": "mlk"}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = 248", "duration": 1.78, "duration_str": "1.78s", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` in (23)", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (23)", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` in (1, 2, 3, 4, 5, 495, 496, 497, 498)", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}]}, {"name": "Webkul\\Category", "models": [], "views": [], "queries": [{"sql": "select * from `categories` where exists (select * from `category_translations` where `categories`.`id` = `category_translations`.`category_id` and `category_translations`.`slug` = 'iphone-ip1212-pro') limit 1", "duration": 2.94, "duration_str": "2.94s", "connection": "mlk"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (13)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 2.52, "duration_str": "2.52s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.7, "duration_str": "1.7s", "connection": "mlk"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 3.09, "duration_str": "3.09s", "connection": "mlk"}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "duration": 1.78, "duration_str": "1.78s", "connection": "mlk"}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 2", "duration": 1.43, "duration_str": "1.43s", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 2.01, "duration_str": "2.01s", "connection": "mlk"}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 order by `name` asc", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (1)"], "views": [], "queries": [{"sql": "select * from `customer_groups` where `code` = 'guest'", "duration": 1.44, "duration_str": "1.44s", "connection": "mlk"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\ProductAttributeValue (79)", "Webkul\\Product\\Models\\Product (8)", "Webkul\\Product\\Models\\ProductImage (7)", "Webkul\\Product\\Models\\ProductInventoryIndex (4)", "Webkul\\Product\\Models\\ProductPriceIndex (12)"], "views": [], "queries": [{"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = 'iphone-ip1212-pro'", "duration": 2.11, "duration_str": "2.11s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 248 limit 1", "duration": 1.75, "duration_str": "1.75s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 248 and `product_attribute_values`.`product_id` is not null", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` = 248 and `product_reviews`.`product_id` is not null", "duration": 1.78, "duration_str": "1.78s", "connection": "mlk"}, {"sql": "select `rating`, count(*) as total from `product_reviews` where `product_reviews`.`product_id` = 248 and `product_reviews`.`product_id` is not null and `status` = 'approved' group by `rating` order by `rating` desc", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 248 and `product_images`.`product_id` is not null order by `position` asc", "duration": 1.74, "duration_str": "1.74s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 248 and `products`.`parent_id` is not null", "duration": 0.32, "duration_str": "320ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 249 and `product_attribute_values`.`product_id` is not null", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 249 and `product_inventory_indices`.`product_id` is not null", "duration": 1.58, "duration_str": "1.58s", "connection": "mlk"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` = 248 and `product_videos`.`product_id` is not null order by `position` asc", "duration": 1.61, "duration_str": "1.61s", "connection": "mlk"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` = 248 and `product_price_indices`.`product_id` is not null", "duration": 1.54, "duration_str": "1.54s", "connection": "mlk"}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 248 and `product_customer_group_prices`.`product_id` is not null and (`customer_group_id` = 1 or `customer_group_id` is null) and `qty` > 1 group by `qty` order by `qty` asc", "duration": 2.14, "duration_str": "2.14s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 248 and `products`.`parent_id` is not null", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` in (248)", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (249, 250, 251)", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (249, 250, 251)", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (249, 250, 251)", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (249, 250, 251) order by `position` asc", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (249, 250, 251) order by `position` asc", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}]}, {"name": "Webkul\\Theme", "models": ["Webkul\\Theme\\Models\\ThemeCustomization (1)", "Webkul\\Theme\\Models\\ThemeCustomizationTranslation (5)"], "views": [], "queries": [{"sql": "select * from `theme_customizations` where `type` = 'services_content' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "duration": 1.83, "duration_str": "1.83s", "connection": "mlk"}, {"sql": "select * from `theme_customizations` where `type` = 'footer_links' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (24)", "duration": 1.59, "duration_str": "1.59s", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": [], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.1, "duration_str": "100ms", "connection": "mlk"}]}]}, "messages": {"count": 3, "messages": [{"message": "[08:47:42] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.927976, "xdebug_link": null, "collector": "log"}, {"message": "[08:47:42] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\3fa9762d568c35d9bc64a16c3ac81557.php on line 17", "message_html": null, "is_string": false, "label": "warning", "time": **********.959501, "xdebug_link": null, "collector": "log"}, {"message": "[08:47:42] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\3fa9762d568c35d9bc64a16c3ac81557.php on line 64", "message_html": null, "is_string": false, "label": "warning", "time": **********.973002, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.683589, "end": **********.143676, "duration": 0.4600870609283447, "duration_str": "460ms", "measures": [{"label": "Booting", "start": **********.683589, "relative_start": 0, "end": **********.831269, "relative_end": **********.831269, "duration": 0.*****************, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.831279, "relative_start": 0.****************, "end": **********.143678, "relative_end": 1.9073486328125e-06, "duration": 0.31239************, "duration_str": "312ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.840307, "relative_start": 0.*****************, "end": **********.84342, "relative_end": **********.84342, "duration": 0.0031130313873291016, "duration_str": "3.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.921796, "relative_start": 0.*****************, "end": **********.134748, "relative_end": **********.134748, "duration": 0.****************, "duration_str": "213ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::products.view", "start": **********.92314, "relative_start": 0.*****************, "end": **********.92314, "relative_end": **********.92314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.view", "start": **********.974175, "relative_start": 0.***************, "end": **********.974175, "relative_end": **********.974175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.gallery", "start": **********.974521, "relative_start": 0.29093194007873535, "end": **********.974521, "relative_end": **********.974521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.accordion.index", "start": **********.97552, "relative_start": 0.2919309139251709, "end": **********.97552, "relative_end": **********.97552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.accordion.index", "start": **********.97743, "relative_start": 0.2938411235809326, "end": **********.97743, "relative_end": **********.97743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.010641, "relative_start": 0.32705211639404297, "end": **********.010641, "relative_end": **********.010641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.011053, "relative_start": 0.32746410369873047, "end": **********.011053, "relative_end": **********.011053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.011398, "relative_start": 0.32780909538269043, "end": **********.011398, "relative_end": **********.011398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.card", "start": **********.011756, "relative_start": 0.3281669616699219, "end": **********.011756, "relative_end": **********.011756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.012395, "relative_start": 0.32880592346191406, "end": **********.012395, "relative_end": **********.012395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.ratings", "start": **********.0133, "relative_start": 0.3297109603881836, "end": **********.0133, "relative_end": **********.0133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.015276, "relative_start": 0.33168697357177734, "end": **********.015276, "relative_end": **********.015276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.ratings", "start": **********.017065, "relative_start": 0.33347606658935547, "end": **********.017065, "relative_end": **********.017065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.button.index", "start": **********.017978, "relative_start": 0.33438897132873535, "end": **********.017978, "relative_end": **********.017978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.018309, "relative_start": 0.3347201347351074, "end": **********.018309, "relative_end": **********.018309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.018474, "relative_start": 0.3348851203918457, "end": **********.018474, "relative_end": **********.018474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.018723, "relative_start": 0.33513402938842773, "end": **********.018723, "relative_end": **********.018723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.018946, "relative_start": 0.3353569507598877, "end": **********.018946, "relative_end": **********.018946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.019095, "relative_start": 0.33550596237182617, "end": **********.019095, "relative_end": **********.019095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.gallery", "start": **********.019494, "relative_start": 0.3359050750732422, "end": **********.019494, "relative_end": **********.019494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.gallery", "start": **********.019741, "relative_start": 0.3361520767211914, "end": **********.019741, "relative_end": **********.019741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.gallery.desktop", "start": **********.019979, "relative_start": 0.33639001846313477, "end": **********.019979, "relative_end": **********.019979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.gallery.mobile", "start": **********.020305, "relative_start": 0.3367159366607666, "end": **********.020305, "relative_end": **********.020305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.gallery", "start": **********.020517, "relative_start": 0.336928129196167, "end": **********.020517, "relative_end": **********.020517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.image-zoomer.index", "start": **********.020765, "relative_start": 0.3371760845184326, "end": **********.020765, "relative_end": **********.020765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.configurable", "start": **********.031306, "relative_start": 0.3477170467376709, "end": **********.031306, "relative_end": **********.031306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.types.simple", "start": **********.036437, "relative_start": 0.3528480529785156, "end": **********.036437, "relative_end": **********.036437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.customizable-options", "start": **********.036758, "relative_start": 0.35316896438598633, "end": **********.036758, "relative_end": **********.036758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.types.configurable", "start": **********.037559, "relative_start": 0.3539700508117676, "end": **********.037559, "relative_end": **********.037559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.types.grouped", "start": **********.07135, "relative_start": 0.38776111602783203, "end": **********.07135, "relative_end": **********.07135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.types.bundle", "start": **********.071777, "relative_start": 0.38818812370300293, "end": **********.071777, "relative_end": **********.071777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.types.downloadable", "start": **********.072298, "relative_start": 0.38870906829833984, "end": **********.072298, "relative_end": **********.072298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.types.booking", "start": **********.072645, "relative_start": 0.3890559673309326, "end": **********.072645, "relative_end": **********.072645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.quantity-changer.index", "start": **********.073032, "relative_start": 0.38944292068481445, "end": **********.073032, "relative_end": **********.073032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.button.index", "start": **********.074678, "relative_start": 0.39108896255493164, "end": **********.074678, "relative_end": **********.074678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.button.index", "start": **********.076177, "relative_start": 0.39258790016174316, "end": **********.076177, "relative_end": **********.076177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::share", "start": **********.076599, "relative_start": 0.39300990104675293, "end": **********.076599, "relative_end": **********.076599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::links.facebook", "start": **********.078499, "relative_start": 0.3949100971221924, "end": **********.078499, "relative_end": **********.078499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::icons.facebook", "start": **********.078858, "relative_start": 0.39526891708374023, "end": **********.078858, "relative_end": **********.078858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::links.twitter", "start": **********.079673, "relative_start": 0.3960840702056885, "end": **********.079673, "relative_end": **********.079673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::icons.twitter", "start": **********.079987, "relative_start": 0.39639806747436523, "end": **********.079987, "relative_end": **********.079987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::links.pinterest", "start": **********.081325, "relative_start": 0.3977360725402832, "end": **********.081325, "relative_end": **********.081325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::icons.pinterest", "start": **********.081697, "relative_start": 0.39810800552368164, "end": **********.081697, "relative_end": **********.081697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::links.linkedin", "start": **********.082487, "relative_start": 0.3988981246948242, "end": **********.082487, "relative_end": **********.082487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::icons.linkedin", "start": **********.082841, "relative_start": 0.39925193786621094, "end": **********.082841, "relative_end": **********.082841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::links.whatsapp", "start": **********.08383, "relative_start": 0.4002411365509033, "end": **********.08383, "relative_end": **********.08383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::icons.whatsapp", "start": **********.084208, "relative_start": 0.4006190299987793, "end": **********.084208, "relative_end": **********.084208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::links.email", "start": **********.085039, "relative_start": 0.40144991874694824, "end": **********.085039, "relative_end": **********.085039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::icons.email", "start": **********.085367, "relative_start": 0.401777982711792, "end": **********.085367, "relative_end": **********.085367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.index", "start": **********.085681, "relative_start": 0.40209197998046875, "end": **********.085681, "relative_end": **********.085681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.index", "start": **********.086164, "relative_start": 0.4025750160217285, "end": **********.086164, "relative_end": **********.086164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.index", "start": **********.089094, "relative_start": 0.4055049419403076, "end": **********.089094, "relative_end": **********.089094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.089449, "relative_start": 0.40585994720458984, "end": **********.089449, "relative_end": **********.089449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.089639, "relative_start": 0.4060499668121338, "end": **********.089639, "relative_end": **********.089639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.modal.confirm", "start": **********.089922, "relative_start": 0.40633296966552734, "end": **********.089922, "relative_end": **********.089922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.index", "start": **********.090278, "relative_start": 0.406688928604126, "end": **********.090278, "relative_end": **********.090278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.top", "start": **********.093278, "relative_start": 0.40968894958496094, "end": **********.093278, "relative_end": **********.093278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.093813, "relative_start": 0.41022396087646484, "end": **********.093813, "relative_end": **********.093813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.094995, "relative_start": 0.4114060401916504, "end": **********.094995, "relative_end": **********.094995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.index", "start": **********.098516, "relative_start": 0.41492700576782227, "end": **********.098516, "relative_end": **********.098516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.bottom", "start": **********.098831, "relative_start": 0.41524195671081543, "end": **********.098831, "relative_end": **********.098831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::search.images.index", "start": **********.101313, "relative_start": 0.4177241325378418, "end": **********.101313, "relative_end": **********.101313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.cart.mini-cart", "start": **********.102965, "relative_start": 0.4193761348724365, "end": **********.102965, "relative_end": **********.102965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.components.option-badges", "start": **********.10545, "relative_start": 0.421860933303833, "end": **********.10545, "relative_end": **********.10545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.quantity-changer.index", "start": **********.105718, "relative_start": 0.42212891578674316, "end": **********.105718, "relative_end": **********.105718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.106279, "relative_start": 0.42268991470336914, "end": **********.106279, "relative_end": **********.106279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.107975, "relative_start": 0.42438602447509766, "end": **********.107975, "relative_end": **********.107975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.108744, "relative_start": 0.4251549243927002, "end": **********.108744, "relative_end": **********.108744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.mobile.index", "start": **********.109178, "relative_start": 0.42558908462524414, "end": **********.109178, "relative_end": **********.109178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.cart.mini-cart", "start": **********.111304, "relative_start": 0.4277150630950928, "end": **********.111304, "relative_end": **********.111304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.111776, "relative_start": 0.42818713188171387, "end": **********.111776, "relative_end": **********.111776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::search.images.index", "start": **********.112519, "relative_start": 0.42893004417419434, "end": **********.112519, "relative_end": **********.112519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.113924, "relative_start": 0.43033504486083984, "end": **********.113924, "relative_end": **********.113924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.114914, "relative_start": 0.43132495880126953, "end": **********.114914, "relative_end": **********.114914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.115106, "relative_start": 0.4315171241760254, "end": **********.115106, "relative_end": **********.115106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.services", "start": **********.116679, "relative_start": 0.4330899715423584, "end": **********.116679, "relative_end": **********.116679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.footer.index", "start": **********.119547, "relative_start": 0.4359579086303711, "end": **********.119547, "relative_end": **********.119547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.accordion.index", "start": **********.130393, "relative_start": 0.4468040466308594, "end": **********.130393, "relative_end": **********.130393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.131421, "relative_start": 0.4478321075439453, "end": **********.131421, "relative_end": **********.131421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.132005, "relative_start": 0.44841599464416504, "end": **********.132005, "relative_end": **********.132005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.index", "start": **********.132239, "relative_start": 0.4486501216888428, "end": **********.132239, "relative_end": **********.132239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core::blade.tracer.style", "start": **********.132859, "relative_start": 0.44927000999450684, "end": **********.132859, "relative_end": **********.132859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: paypal::checkout.onepage.paypal-smart-button", "start": **********.133375, "relative_start": 0.4497859477996826, "end": **********.133375, "relative_end": **********.133375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 47039440, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 83, "nb_templates": 83, "templates": [{"name": "1x shop::products.view", "param_count": null, "params": [], "start": **********.923125, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.phpshop::products.view", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=1", "ajax": false, "filename": "view.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view"}, {"name": "1x shop::components.shimmer.products.view", "param_count": null, "params": [], "start": **********.974165, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/view.blade.phpshop::components.shimmer.products.view", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fview.blade.php&line=1", "ajax": false, "filename": "view.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.shimmer.products.view"}, {"name": "3x shop::components.shimmer.products.gallery", "param_count": null, "params": [], "start": **********.974512, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/gallery.blade.phpshop::components.shimmer.products.gallery", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fgallery.blade.php&line=1", "ajax": false, "filename": "gallery.blade.php", "line": "?"}, "render_count": 3, "name_original": "shop::components.shimmer.products.gallery"}, {"name": "3x shop::components.accordion.index", "param_count": null, "params": [], "start": **********.975511, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/accordion/index.blade.phpshop::components.accordion.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Faccordion%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "shop::components.accordion.index"}, {"name": "2x shop::components.products.carousel", "param_count": null, "params": [], "start": **********.01063, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/products/carousel.blade.phpshop::components.products.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.products.carousel"}, {"name": "3x shop::components.shimmer.products.carousel", "param_count": null, "params": [], "start": **********.011044, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/carousel.blade.phpshop::components.shimmer.products.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 3, "name_original": "shop::components.shimmer.products.carousel"}, {"name": "3x shop::components.shimmer.products.cards.grid", "param_count": null, "params": [], "start": **********.011389, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/cards/grid.blade.phpshop::components.shimmer.products.cards.grid", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fcards%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}, "render_count": 3, "name_original": "shop::components.shimmer.products.cards.grid"}, {"name": "1x shop::components.products.card", "param_count": null, "params": [], "start": **********.011748, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/products/card.blade.phpshop::components.products.card", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.products.card"}, {"name": "2x shop::components.media.images.lazy", "param_count": null, "params": [], "start": **********.012386, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/media/images/lazy.blade.phpshop::components.media.images.lazy", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmedia%2Fimages%2Flazy.blade.php&line=1", "ajax": false, "filename": "lazy.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.media.images.lazy"}, {"name": "2x shop::components.products.ratings", "param_count": null, "params": [], "start": **********.01329, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/products/ratings.blade.phpshop::components.products.ratings", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fratings.blade.php&line=1", "ajax": false, "filename": "ratings.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.products.ratings"}, {"name": "3x shop::components.button.index", "param_count": null, "params": [], "start": **********.017969, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/button/index.blade.phpshop::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "shop::components.button.index"}, {"name": "1x shop::products.view.gallery", "param_count": null, "params": [], "start": **********.019485, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/gallery.blade.phpshop::products.view.gallery", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Fgallery.blade.php&line=1", "ajax": false, "filename": "gallery.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.gallery"}, {"name": "1x shop::products.view.gallery.desktop", "param_count": null, "params": [], "start": **********.01997, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/gallery/desktop.blade.phpshop::products.view.gallery.desktop", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Fgallery%2Fdesktop.blade.php&line=1", "ajax": false, "filename": "desktop.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.gallery.desktop"}, {"name": "1x shop::products.view.gallery.mobile", "param_count": null, "params": [], "start": **********.020296, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/gallery/mobile.blade.phpshop::products.view.gallery.mobile", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Fgallery%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.gallery.mobile"}, {"name": "1x shop::components.image-zoomer.index", "param_count": null, "params": [], "start": **********.020756, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/image-zoomer/index.blade.phpshop::components.image-zoomer.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fimage-zoomer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.image-zoomer.index"}, {"name": "1x shop::products.prices.configurable", "param_count": null, "params": [], "start": **********.031294, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/prices/configurable.blade.phpshop::products.prices.configurable", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fconfigurable.blade.php&line=1", "ajax": false, "filename": "configurable.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.prices.configurable"}, {"name": "1x shop::products.view.types.simple", "param_count": null, "params": [], "start": **********.036426, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/simple.blade.phpshop::products.view.types.simple", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Ftypes%2Fsimple.blade.php&line=1", "ajax": false, "filename": "simple.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.types.simple"}, {"name": "1x shop::products.view.customizable-options", "param_count": null, "params": [], "start": **********.036749, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/customizable-options.blade.phpshop::products.view.customizable-options", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Fcustomizable-options.blade.php&line=1", "ajax": false, "filename": "customizable-options.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.customizable-options"}, {"name": "1x shop::products.view.types.configurable", "param_count": null, "params": [], "start": **********.03755, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/configurable.blade.phpshop::products.view.types.configurable", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Ftypes%2Fconfigurable.blade.php&line=1", "ajax": false, "filename": "configurable.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.types.configurable"}, {"name": "1x shop::products.view.types.grouped", "param_count": null, "params": [], "start": **********.071339, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/grouped.blade.phpshop::products.view.types.grouped", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Ftypes%2Fgrouped.blade.php&line=1", "ajax": false, "filename": "grouped.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.types.grouped"}, {"name": "1x shop::products.view.types.bundle", "param_count": null, "params": [], "start": **********.071768, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/bundle.blade.phpshop::products.view.types.bundle", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Ftypes%2Fbundle.blade.php&line=1", "ajax": false, "filename": "bundle.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.types.bundle"}, {"name": "1x shop::products.view.types.downloadable", "param_count": null, "params": [], "start": **********.072289, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/downloadable.blade.phpshop::products.view.types.downloadable", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Ftypes%2Fdownloadable.blade.php&line=1", "ajax": false, "filename": "downloadable.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.types.downloadable"}, {"name": "1x shop::products.view.types.booking", "param_count": null, "params": [], "start": **********.072635, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/booking.blade.phpshop::products.view.types.booking", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Ftypes%2Fbooking.blade.php&line=1", "ajax": false, "filename": "booking.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.types.booking"}, {"name": "2x shop::components.quantity-changer.index", "param_count": null, "params": [], "start": **********.073023, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/quantity-changer/index.blade.phpshop::components.quantity-changer.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fquantity-changer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.quantity-changer.index"}, {"name": "1x social_share::share", "param_count": null, "params": [], "start": **********.076589, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/share.blade.phpsocial_share::share", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Fshare.blade.php&line=1", "ajax": false, "filename": "share.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::share"}, {"name": "1x social_share::links.facebook", "param_count": null, "params": [], "start": **********.078489, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/links/facebook.blade.phpsocial_share::links.facebook", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Flinks%2Ffacebook.blade.php&line=1", "ajax": false, "filename": "facebook.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::links.facebook"}, {"name": "1x social_share::icons.facebook", "param_count": null, "params": [], "start": **********.078849, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/icons/facebook.blade.phpsocial_share::icons.facebook", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Ficons%2Ffacebook.blade.php&line=1", "ajax": false, "filename": "facebook.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::icons.facebook"}, {"name": "1x social_share::links.twitter", "param_count": null, "params": [], "start": **********.079663, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/links/twitter.blade.phpsocial_share::links.twitter", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Flinks%2Ftwitter.blade.php&line=1", "ajax": false, "filename": "twitter.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::links.twitter"}, {"name": "1x social_share::icons.twitter", "param_count": null, "params": [], "start": **********.079979, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/icons/twitter.blade.phpsocial_share::icons.twitter", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Ficons%2Ftwitter.blade.php&line=1", "ajax": false, "filename": "twitter.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::icons.twitter"}, {"name": "1x social_share::links.pinterest", "param_count": null, "params": [], "start": **********.081315, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/links/pinterest.blade.phpsocial_share::links.pinterest", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Flinks%2Fpinterest.blade.php&line=1", "ajax": false, "filename": "pinterest.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::links.pinterest"}, {"name": "1x social_share::icons.pinterest", "param_count": null, "params": [], "start": **********.081688, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/icons/pinterest.blade.phpsocial_share::icons.pinterest", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Ficons%2Fpinterest.blade.php&line=1", "ajax": false, "filename": "pinterest.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::icons.pinterest"}, {"name": "1x social_share::links.linkedin", "param_count": null, "params": [], "start": **********.082478, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/links/linkedin.blade.phpsocial_share::links.linkedin", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Flinks%2Flinkedin.blade.php&line=1", "ajax": false, "filename": "linkedin.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::links.linkedin"}, {"name": "1x social_share::icons.linkedin", "param_count": null, "params": [], "start": **********.082832, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/icons/linkedin.blade.phpsocial_share::icons.linkedin", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Ficons%2Flinkedin.blade.php&line=1", "ajax": false, "filename": "linkedin.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::icons.linkedin"}, {"name": "1x social_share::links.whatsapp", "param_count": null, "params": [], "start": **********.08382, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/links/whatsapp.blade.phpsocial_share::links.whatsapp", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Flinks%2Fwhatsapp.blade.php&line=1", "ajax": false, "filename": "whatsapp.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::links.whatsapp"}, {"name": "1x social_share::icons.whatsapp", "param_count": null, "params": [], "start": **********.084199, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/icons/whatsapp.blade.phpsocial_share::icons.whatsapp", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Ficons%2Fwhatsapp.blade.php&line=1", "ajax": false, "filename": "whatsapp.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::icons.whatsapp"}, {"name": "1x social_share::links.email", "param_count": null, "params": [], "start": **********.08503, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/links/email.blade.phpsocial_share::links.email", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Flinks%2Femail.blade.php&line=1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::links.email"}, {"name": "1x social_share::icons.email", "param_count": null, "params": [], "start": **********.085358, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/icons/email.blade.phpsocial_share::icons.email", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Ficons%2Femail.blade.php&line=1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::icons.email"}, {"name": "2x shop::components.form.index", "param_count": null, "params": [], "start": **********.085672, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/form/index.blade.phpshop::components.form.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.form.index"}, {"name": "1x shop::components.layouts.index", "param_count": null, "params": [], "start": **********.086155, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/index.blade.phpshop::components.layouts.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.index"}, {"name": "1x shop::components.flash-group.index", "param_count": null, "params": [], "start": **********.089085, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/index.blade.phpshop::components.flash-group.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.flash-group.index"}, {"name": "2x shop::components.flash-group.item", "param_count": null, "params": [], "start": **********.08944, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/item.blade.phpshop::components.flash-group.item", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.flash-group.item"}, {"name": "1x shop::components.modal.confirm", "param_count": null, "params": [], "start": **********.089913, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/modal/confirm.blade.phpshop::components.modal.confirm", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.modal.confirm"}, {"name": "1x shop::components.layouts.header.index", "param_count": null, "params": [], "start": **********.090269, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.phpshop::components.layouts.header.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.index"}, {"name": "1x shop::components.layouts.header.desktop.top", "param_count": null, "params": [], "start": **********.093269, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.phpshop::components.layouts.header.desktop.top", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=1", "ajax": false, "filename": "top.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.top"}, {"name": "4x shop::components.dropdown.index", "param_count": null, "params": [], "start": **********.093804, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/dropdown/index.blade.phpshop::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.dropdown.index"}, {"name": "1x shop::components.layouts.header.desktop.index", "param_count": null, "params": [], "start": **********.098507, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/index.blade.phpshop::components.layouts.header.desktop.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.index"}, {"name": "1x shop::components.layouts.header.desktop.bottom", "param_count": null, "params": [], "start": **********.098823, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.phpshop::components.layouts.header.desktop.bottom", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Fbottom.blade.php&line=1", "ajax": false, "filename": "bottom.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.bottom"}, {"name": "2x shop::search.images.index", "param_count": null, "params": [], "start": **********.1013, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/search/images/index.blade.phpshop::search.images.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fsearch%2Fimages%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::search.images.index"}, {"name": "2x shop::checkout.cart.mini-cart", "param_count": null, "params": [], "start": **********.102955, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/checkout/cart/mini-cart.blade.phpshop::checkout.cart.mini-cart", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fcart%2Fmini-cart.blade.php&line=1", "ajax": false, "filename": "mini-cart.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::checkout.cart.mini-cart"}, {"name": "1x shop::checkout.components.option-badges", "param_count": null, "params": [], "start": **********.105441, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/checkout/components/option-badges.blade.phpshop::checkout.components.option-badges", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fcomponents%2Foption-badges.blade.php&line=1", "ajax": false, "filename": "option-badges.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::checkout.components.option-badges"}, {"name": "5x shop::components.drawer.index", "param_count": null, "params": [], "start": **********.106271, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/drawer/index.blade.phpshop::components.drawer.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdrawer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.drawer.index"}, {"name": "1x shop::components.layouts.header.mobile.index", "param_count": null, "params": [], "start": **********.109169, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.phpshop::components.layouts.header.mobile.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.mobile.index"}, {"name": "1x shop::components.layouts.services", "param_count": null, "params": [], "start": **********.11667, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.phpshop::components.layouts.services", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fservices.blade.php&line=1", "ajax": false, "filename": "services.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.services"}, {"name": "1x shop::components.layouts.footer.index", "param_count": null, "params": [], "start": **********.119538, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.phpshop::components.layouts.footer.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Ffooter%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.footer.index"}, {"name": "1x shop::components.form.control-group.control", "param_count": null, "params": [], "start": **********.131411, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/control.blade.phpshop::components.form.control-group.control", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Fcontrol.blade.php&line=1", "ajax": false, "filename": "control.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.form.control-group.control"}, {"name": "1x shop::components.form.control-group.error", "param_count": null, "params": [], "start": **********.131996, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/error.blade.phpshop::components.form.control-group.error", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.form.control-group.error"}, {"name": "1x core::blade.tracer.style", "param_count": null, "params": [], "start": **********.132848, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src/resources/views/blade/tracer/style.blade.phpcore::blade.tracer.style", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FResources%2Fviews%2Fblade%2Ftracer%2Fstyle.blade.php&line=1", "ajax": false, "filename": "style.blade.php", "line": "?"}, "render_count": 1, "name_original": "core::blade.tracer.style"}, {"name": "1x paypal::checkout.onepage.paypal-smart-button", "param_count": null, "params": [], "start": **********.133358, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Paypal\\src/resources/views/checkout/onepage/paypal-smart-button.blade.phppaypal::checkout.onepage.paypal-smart-button", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FPaypal%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fonepage%2Fpaypal-smart-button.blade.php&line=1", "ajax": false, "filename": "paypal-smart-button.blade.php", "line": "?"}, "render_count": 1, "name_original": "paypal::checkout.onepage.paypal-smart-button"}]}, "queries": {"count": 95, "nb_statements": 95, "nb_visible_statements": 95, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06358000000000001, "accumulated_duration_str": "63.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "theme", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.85826, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 3.964}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.863616, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 3.964, "width_percent": 2.674}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.866393, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 6.637, "width_percent": 4.86}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.873304, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 11.497, "width_percent": 0.645}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.874172, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 12.142, "width_percent": 0.252}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.874817, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 12.394, "width_percent": 0.283}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.875358, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 12.677, "width_percent": 0.157}, {"sql": "select * from `categories` where exists (select * from `category_translations` where `categories`.`id` = `category_translations`.`category_id` and `category_translations`.`slug` = 'iphone-ip1212-pro') limit 1", "type": "query", "params": [], "bindings": ["iphone-ip1212-pro"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 213}, {"index": 17, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/ProductsCategoriesProxyController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\ProductsCategoriesProxyController.php", "line": 55}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.878797, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "CategoryRepository.php:213", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 213}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FRepositories%2FCategoryRepository.php&line=213", "ajax": false, "filename": "CategoryRepository.php", "line": "213"}, "connection": "mlk", "explain": null, "start_percent": 12.834, "width_percent": 4.624}, {"sql": "select * from `attributes` where `code` = 'url_key'", "type": "query", "params": [], "bindings": ["url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 125}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}], "start": **********.883789, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 17.458, "width_percent": 0.315}, {"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = 'iphone-ip1212-pro'", "type": "query", "params": [], "bindings": [3, "iphone-ip1212-pro"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}, {"index": 19, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/ProductsCategoriesProxyController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\ProductsCategoriesProxyController.php", "line": 76}], "start": **********.884643, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 17.773, "width_percent": 3.319}, {"sql": "select * from `products` where `products`.`id` = 248 limit 1", "type": "query", "params": [], "bindings": [248], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 161}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/ProductsCategoriesProxyController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\ProductsCategoriesProxyController.php", "line": 76}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.887702, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:161", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductRepository.php&line=161", "ajax": false, "filename": "ProductRepository.php", "line": "161"}, "connection": "mlk", "explain": null, "start_percent": 21.092, "width_percent": 2.752}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/ProductsCategoriesProxyController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\ProductsCategoriesProxyController.php", "line": 80}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.890042, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 23.844, "width_percent": 2.658}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 190}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/ProductsCategoriesProxyController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\ProductsCategoriesProxyController.php", "line": 80}], "start": **********.892255, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mlk", "explain": null, "start_percent": 26.502, "width_percent": 2.485}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 248 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [248], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/ProductsCategoriesProxyController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\ProductsCategoriesProxyController.php", "line": 80}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.895152, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 28.987, "width_percent": 0.425}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"eb333224-0e61-4c4a-8f1f-6523d6069413\\\",\\\"displayName\\\":\\\"Webkul\\\\\\\\Core\\\\\\\\Jobs\\\\\\\\UpdateCreateVisitIndex\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Webkul\\\\\\\\Core\\\\\\\\Jobs\\\\\\\\UpdateCreateVisitIndex\\\",\\\"command\\\":\\\"O:39:\\\\\\\"Webkul\\\\\\\\Core\\\\\\\\Jobs\\\\\\\\UpdateCreateVisitIndex\\\\\\\":2:{s:8:\\\\\\\"\\\\u0000*\\\\u0000model\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:29:\\\\\\\"Webkul\\\\\\\\Product\\\\\\\\Models\\\\\\\\Product\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:248;s:9:\\\\\\\"relations\\\\\\\";a:2:{i:0;s:16:\\\\\\\"attribute_family\\\\\\\";i:1;s:16:\\\\\\\"attribute_values\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:6:\\\\\\\"\\\\u0000*\\\\u0000log\\\\\\\";a:14:{s:6:\\\\\\\"method\\\\\\\";s:3:\\\\\\\"GET\\\\\\\";s:7:\\\\\\\"request\\\\\\\";a:0:{}s:3:\\\\\\\"url\\\\\\\";s:33:\\\\\\\"http:\\\\/\\\\/mlk.test\\\\/iphone-ip1212-pro\\\\\\\";s:7:\\\\\\\"referer\\\\\\\";s:16:\\\\\\\"http:\\\\/\\\\/mlk.test\\\\/\\\\\\\";s:9:\\\\\\\"languages\\\\\\\";a:0:{}s:9:\\\\\\\"useragent\\\\\\\";s:111:\\\\\\\"Mozilla\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\/537.36 (KHTML, like Gecko) Chrome\\\\/********* Safari\\\\/537.36\\\\\\\";s:7:\\\\\\\"headers\\\\\\\";a:9:{s:6:\\\\\\\"cookie\\\\\\\";a:1:{i:0;s:783:\\\\\\\"sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IkNaRnhyYkZwVUZDR2pUazNORzVsVUE9PSIsInZhbHVlIjoiOE5nVjVFOVRsOWFpSmJ0SjFpV3RpUFExSzhZWmRjcm96U2xIb0RvWjZ6dzRqQjJwckllQlpiVnB2YVdaTkpiUFRtM0ZocXdocTNxajBPQWtuYmJXWitOU2lXdFIrK3J2REpqRkpGVDlmS0Q5endNMEdwcXlTellGUzd3OE8zU00iLCJtYWMiOiI0ODhhNzRiNzI4ZjFhYzQwYTNiMDAwZTcwMTk3MGUyM2I4Nzc0ZTk4Y2RhZmY3YmEyMDE2NWFhYThkMjhiYTQ3IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Im1tdHVjdm4wWFpZeStHd3lzaUZMQmc9PSIsInZhbHVlIjoiUWpPTExZclBTME5HOUZRd0NST1MvNFBiQWFPcXM5QXVKT2hYRTZqMFMwbzVwOTYzL1cwRHJoM3EyYW1xM045RVA5ZzJ0UnJsVDdCam1HWFVpeGZRb2RDeUZZWUc2dUdCdHI2SVVjU0V1eTE4emJNQVZia042R3FwdjJqYXp5VDEiLCJtYWMiOiIzYmRhMGVhZDQ5ZmUwZGQ3N2RjYjExZDI4YTM4NGYyOWEyNmRjM2Y4NWRkM2E1OGJlNzEyNmE4MTk0Nzg3MDgyIiwidGFnIjoiIn0%3D\\\\\\\";}s:15:\\\\\\\"accept-language\\\\\\\";a:1:{i:0;s:23:\\\\\\\"zh-CN,zh;q=0.9,en;q=0.8\\\\\\\";}s:15:\\\\\\\"accept-encoding\\\\\\\";a:1:{i:0;s:13:\\\\\\\"gzip, deflate\\\\\\\";}s:7:\\\\\\\"referer\\\\\\\";a:1:{i:0;s:16:\\\\\\\"http:\\\\/\\\\/mlk.test\\\\/\\\\\\\";}s:6:\\\\\\\"accept\\\\\\\";a:1:{i:0;s:135:\\\\\\\"text\\\\/html,application\\\\/xhtml+xml,application\\\\/xml;q=0.9,image\\\\/avif,image\\\\/webp,image\\\\/apng,*\\\\/*;q=0.8,application\\\\/signed-exchange;v=b3;q=0.7\\\\\\\";}s:10:\\\\\\\"user-agent\\\\\\\";a:1:{i:0;s:111:\\\\\\\"Mozilla\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\/537.36 (KHTML, like Gecko) Chrome\\\\/********* Safari\\\\/537.36\\\\\\\";}s:25:\\\\\\\"upgrade-insecure-requests\\\\\\\";a:1:{i:0;s:1:\\\\\\\"1\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";a:1:{i:0;s:10:\\\\\\\"keep-alive\\\\\\\";}s:4:\\\\\\\"host\\\\\\\";a:1:{i:0;s:8:\\\\\\\"mlk.test\\\\\\\";}}s:6:\\\\\\\"device\\\\\\\";s:0:\\\\\\\"\\\\\\\";s:8:\\\\\\\"platform\\\\\\\";s:7:\\\\\\\"Windows\\\\\\\";s:7:\\\\\\\"browser\\\\\\\";s:6:\\\\\\\"Chrome\\\\\\\";s:2:\\\\\\\"ip\\\\\\\";s:9:\\\\\\\"127.0.0.1\\\\\\\";s:10:\\\\\\\"visitor_id\\\\\\\";N;s:12:\\\\\\\"visitor_type\\\\\\\";N;s:10:\\\\\\\"channel_id\\\\\\\";i:1;}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"eb333224-0e61-4c4a-8f1f-6523d6069413\",\"displayName\":\"Webkul\\\\Core\\\\Jobs\\\\UpdateCreateVisitIndex\",\"job\":\"Illuminate\\\\Queue\\\\CallQueued<PERSON><PERSON><PERSON>@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Webkul\\\\Core\\\\Jobs\\\\UpdateCreateVisitIndex\",\"command\":\"O:39:\\\"Webkul\\\\Core\\\\Jobs\\\\UpdateCreateVisitIndex\\\":2:{s:8:\\\"\\u0000*\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:29:\\\"Webkul\\\\Product\\\\Models\\\\Product\\\";s:2:\\\"id\\\";i:248;s:9:\\\"relations\\\";a:2:{i:0;s:16:\\\"attribute_family\\\";i:1;s:16:\\\"attribute_values\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:6:\\\"\\u0000*\\u0000log\\\";a:14:{s:6:\\\"method\\\";s:3:\\\"GET\\\";s:7:\\\"request\\\";a:0:{}s:3:\\\"url\\\";s:33:\\\"http:\\/\\/mlk.test\\/iphone-ip1212-pro\\\";s:7:\\\"referer\\\";s:16:\\\"http:\\/\\/mlk.test\\/\\\";s:9:\\\"languages\\\";a:0:{}s:9:\\\"useragent\\\";s:111:\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\";s:7:\\\"headers\\\";a:9:{s:6:\\\"cookie\\\";a:1:{i:0;s:783:\\\"sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IkNaRnhyYkZwVUZDR2pUazNORzVsVUE9PSIsInZhbHVlIjoiOE5nVjVFOVRsOWFpSmJ0SjFpV3RpUFExSzhZWmRjcm96U2xIb0RvWjZ6dzRqQjJwckllQlpiVnB2YVdaTkpiUFRtM0ZocXdocTNxajBPQWtuYmJXWitOU2lXdFIrK3J2REpqRkpGVDlmS0Q5endNMEdwcXlTellGUzd3OE8zU00iLCJtYWMiOiI0ODhhNzRiNzI4ZjFhYzQwYTNiMDAwZTcwMTk3MGUyM2I4Nzc0ZTk4Y2RhZmY3YmEyMDE2NWFhYThkMjhiYTQ3IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Im1tdHVjdm4wWFpZeStHd3lzaUZMQmc9PSIsInZhbHVlIjoiUWpPTExZclBTME5HOUZRd0NST1MvNFBiQWFPcXM5QXVKT2hYRTZqMFMwbzVwOTYzL1cwRHJoM3EyYW1xM045RVA5ZzJ0UnJsVDdCam1HWFVpeGZRb2RDeUZZWUc2dUdCdHI2SVVjU0V1eTE4emJNQVZia042R3FwdjJqYXp5VDEiLCJtYWMiOiIzYmRhMGVhZDQ5ZmUwZGQ3N2RjYjExZDI4YTM4NGYyOWEyNmRjM2Y4NWRkM2E1OGJlNzEyNmE4MTk0Nzg3MDgyIiwidGFnIjoiIn0%3D\\\";}s:15:\\\"accept-language\\\";a:1:{i:0;s:23:\\\"zh-CN,zh;q=0.9,en;q=0.8\\\";}s:15:\\\"accept-encoding\\\";a:1:{i:0;s:13:\\\"gzip, deflate\\\";}s:7:\\\"referer\\\";a:1:{i:0;s:16:\\\"http:\\/\\/mlk.test\\/\\\";}s:6:\\\"accept\\\";a:1:{i:0;s:135:\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\";}s:10:\\\"user-agent\\\";a:1:{i:0;s:111:\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\";}s:25:\\\"upgrade-insecure-requests\\\";a:1:{i:0;s:1:\\\"1\\\";}s:10:\\\"connection\\\";a:1:{i:0;s:10:\\\"keep-alive\\\";}s:4:\\\"host\\\";a:1:{i:0;s:8:\\\"mlk.test\\\";}}s:6:\\\"device\\\";s:0:\\\"\\\";s:8:\\\"platform\\\";s:7:\\\"Windows\\\";s:7:\\\"browser\\\";s:6:\\\"Chrome\\\";s:2:\\\"ip\\\";s:9:\\\"127.0.0.1\\\";s:10:\\\"visitor_id\\\";N;s:12:\\\"visitor_type\\\";N;s:10:\\\"channel_id\\\";i:1;}}\"}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 188}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 99}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 338}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 244}], "start": **********.915684, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:188", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=188", "ajax": false, "filename": "DatabaseQueue.php", "line": "188"}, "connection": "mlk", "explain": null, "start_percent": 29.412, "width_percent": 6.622}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` = 248 and `product_reviews`.`product_id` is not null", "type": "query", "params": [], "bindings": [248], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Review.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php", "line": 28}, {"index": 22, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 5}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.924865, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 36.033, "width_percent": 2.8}, {"sql": "select `rating`, count(*) as total from `product_reviews` where `product_reviews`.`product_id` = 248 and `product_reviews`.`product_id` is not null and `status` = 'approved' group by `rating` order by `rating` desc", "type": "query", "params": [], "bindings": [248, "approved"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Review.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php", "line": 79}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Review.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php", "line": 90}, {"index": 18, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 7}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.928408, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Review.php:79", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Review.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FReview.php&line=79", "ajax": false, "filename": "Review.php", "line": "79"}, "connection": "mlk", "explain": null, "start_percent": 38.833, "width_percent": 0.346}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2 and `attributes`.`is_visible_on_front` = 1", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 19}, {"index": 16, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 9}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.929214, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "View.php:19", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FView.php&line=19", "ajax": false, "filename": "View.php", "line": "19"}, "connection": "mlk", "explain": null, "start_percent": 39.179, "width_percent": 0.488}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 23 and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 60}], "start": **********.932539, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 39.667, "width_percent": 3.429}, {"sql": "select * from `attribute_options` where `attribute_options`.`id` = 371 limit 1", "type": "query", "params": [], "bindings": [371], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 30}, {"index": 21, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 9}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.937128, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 43.095, "width_percent": 2.721}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 371 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [371], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 33}], "start": **********.9400978, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 45.816, "width_percent": 2.611}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 25 and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 60}], "start": **********.943536, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 48.427, "width_percent": 0.362}, {"sql": "select * from `attribute_options` where `id` in ('395')", "type": "query", "params": [], "bindings": ["395"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 45}, {"index": 17, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 9}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.945412, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 48.789, "width_percent": 0.283}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 395 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [395], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 48}], "start": **********.946624, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 49.072, "width_percent": 0.283}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 33 and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 60}], "start": **********.948295, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 49.355, "width_percent": 0.267}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 38 and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 60}], "start": **********.95162, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 49.623, "width_percent": 0.566}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 39 and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 60}], "start": **********.9545958, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 50.189, "width_percent": 0.393}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 215}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 470}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 26, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 17}], "start": **********.95714, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 50.582, "width_percent": 2.8}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 248 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [248], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/ProductImage.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\ProductImage.php", "line": 109}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/ProductImage.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\ProductImage.php", "line": 98}, {"index": 23, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 28}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.962362, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 53.382, "width_percent": 2.737}, {"sql": "select * from `attributes` where `attributes`.`id` = 9 limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9777741, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 56.118, "width_percent": 0.472}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9784958, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 56.59, "width_percent": 0.33}, {"sql": "select * from `attributes` where `attributes`.`id` = 38 limit 1", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.979058, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 56.92, "width_percent": 0.252}, {"sql": "select * from `attributes` where `attributes`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.979537, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 57.172, "width_percent": 0.283}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.980035, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 57.455, "width_percent": 0.22}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.98048, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 57.675, "width_percent": 0.22}, {"sql": "select * from `attributes` where `attributes`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.981008, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 57.896, "width_percent": 0.315}, {"sql": "select * from `attributes` where `attributes`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.981624, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 58.21, "width_percent": 0.346}, {"sql": "select * from `attributes` where `attributes`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.982191, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 58.556, "width_percent": 0.252}, {"sql": "select * from `attributes` where `attributes`.`id` = 33 limit 1", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.982709, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 58.808, "width_percent": 0.33}, {"sql": "select * from `attributes` where `attributes`.`id` = 34 limit 1", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.983283, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 59.138, "width_percent": 0.566}, {"sql": "select * from `attributes` where `attributes`.`id` = 39 limit 1", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.984092, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 59.704, "width_percent": 0.393}, {"sql": "select * from `attributes` where `attributes`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.984696, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 60.098, "width_percent": 0.299}, {"sql": "select * from `attributes` where `attributes`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.98527, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 60.396, "width_percent": 0.267}, {"sql": "select * from `attributes` where `attributes`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.985774, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 60.664, "width_percent": 0.236}, {"sql": "select * from `attributes` where `attributes`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.986273, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 60.9, "width_percent": 0.283}, {"sql": "select * from `attributes` where `attributes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.986849, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 61.183, "width_percent": 0.267}, {"sql": "select * from `attributes` where `attributes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.987338, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 61.45, "width_percent": 0.22}, {"sql": "select * from `attributes` where `attributes`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.987786, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 61.67, "width_percent": 0.22}, {"sql": "select * from `attributes` where `attributes`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.988242, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 61.891, "width_percent": 0.315}, {"sql": "select * from `attributes` where `attributes`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.988822, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 62.205, "width_percent": 0.252}, {"sql": "select * from `attributes` where `attributes`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.989303, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 62.457, "width_percent": 0.22}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.989747, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 62.677, "width_percent": 0.252}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.990243, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 62.929, "width_percent": 0.299}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9908621, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 63.227, "width_percent": 0.252}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.991417, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 63.479, "width_percent": 0.299}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.992009, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 63.778, "width_percent": 0.252}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.992559, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 64.03, "width_percent": 0.299}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.993069, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 64.328, "width_percent": 0.22}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.993521, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 64.549, "width_percent": 0.252}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9939811, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 64.8, "width_percent": 0.204}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.994415, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 65.005, "width_percent": 0.189}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.994837, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 65.193, "width_percent": 0.189}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.995266, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "shop::products.view:265", "source": {"index": 28, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=265", "ajax": false, "filename": "view.blade.php", "line": "265"}, "connection": "mlk", "explain": null, "start_percent": 65.382, "width_percent": 0.299}, {"sql": "select * from `attribute_options` where `attribute_options`.`id` = 371 limit 1", "type": "query", "params": [], "bindings": [371], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 288}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.9966269, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 65.681, "width_percent": 0.33}, {"sql": "select * from `products` where `products`.`parent_id` = 248 and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [248], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 592}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}, {"index": 23, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 310}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0003622, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 66.011, "width_percent": 0.503}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.003295, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 66.515, "width_percent": 0.362}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 249 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [249], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.0046132, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 66.876, "width_percent": 0.472}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 249 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [249], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 700}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 507}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 156}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.005703, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 67.348, "width_percent": 2.485}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` = 248 and `product_videos`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [248], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/ProductVideo.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\ProductVideo.php", "line": 23}, {"index": 22, "namespace": "view", "name": "shop::products.view.gallery", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/gallery.blade.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.021501, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 69.833, "width_percent": 2.532}, {"sql": "select * from `customer_groups` where `code` = 'guest'", "type": "query", "params": [], "bindings": ["guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 746}, {"index": 19, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}], "start": **********.024627, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 72.366, "width_percent": 2.265}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` = 248 and `product_price_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [248], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 684}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 600}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 338}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 357}], "start": **********.026804, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 74.63, "width_percent": 2.422}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 457}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 479}], "start": **********.028889, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 77.053, "width_percent": 2.249}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 248 and `product_customer_group_prices`.`product_id` is not null and (`customer_group_id` = 1 or `customer_group_id` is null) and `qty` > 1 group by `qty` order by `qty` asc", "type": "query", "params": [], "bindings": [248, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 986}, {"index": 17, "namespace": "view", "name": "shop::products.view", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 588}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.032642, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:986", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 986}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=986", "ajax": false, "filename": "AbstractType.php", "line": "986"}, "connection": "mlk", "explain": null, "start_percent": 79.302, "width_percent": 3.366}, {"sql": "select * from `products` where `products`.`parent_id` = 248 and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [248], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 18, "namespace": "view", "name": "shop::products.view.types.configurable", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/configurable.blade.php", "line": 278}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.038853, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 82.668, "width_percent": 0.33}, {"sql": "select * from `products` where `products`.`id` in (248)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": "view", "name": "shop::products.view.types.configurable", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/configurable.blade.php", "line": 278}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.039553, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 82.998, "width_percent": 0.315}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (249, 250, 251)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": "view", "name": "shop::products.view.types.configurable", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/configurable.blade.php", "line": 278}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0401702, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 83.312, "width_percent": 0.425}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (249, 250, 251)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": "view", "name": "shop::products.view.types.configurable", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/configurable.blade.php", "line": 278}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.041046, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 83.737, "width_percent": 0.267}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (249, 250, 251)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": "view", "name": "shop::products.view.types.configurable", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/configurable.blade.php", "line": 278}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.041671, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 84.004, "width_percent": 0.252}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (249, 250, 251) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": "view", "name": "shop::products.view.types.configurable", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/configurable.blade.php", "line": 278}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.042263, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 84.256, "width_percent": 0.33}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (249, 250, 251) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": "view", "name": "shop::products.view.types.configurable", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/configurable.blade.php", "line": 278}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.04303, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 84.586, "width_percent": 0.283}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 140}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.044125, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 84.869, "width_percent": 0.315}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 140}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.046725, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 85.184, "width_percent": 0.236}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 140}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.049334, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 85.42, "width_percent": 0.535}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = 248", "type": "query", "params": [], "bindings": [248], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 105}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 18, "namespace": "view", "name": "shop::products.view.types.configurable", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/configurable.blade.php", "line": 278}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.051775, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:91", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=91", "ajax": false, "filename": "ConfigurableOption.php", "line": "91"}, "connection": "mlk", "explain": null, "start_percent": 85.955, "width_percent": 2.8}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 105}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": "view", "name": "shop::products.view.types.configurable", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/configurable.blade.php", "line": 278}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.054133, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:91", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=91", "ajax": false, "filename": "ConfigurableOption.php", "line": "91"}, "connection": "mlk", "explain": null, "start_percent": 88.754, "width_percent": 0.283}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 105}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": "view", "name": "shop::products.view.types.configurable", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/configurable.blade.php", "line": 278}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.054836, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:91", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=91", "ajax": false, "filename": "ConfigurableOption.php", "line": "91"}, "connection": "mlk", "explain": null, "start_percent": 89.037, "width_percent": 0.252}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` in (1, 2, 3, 4, 5, 495, 496, 497, 498)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 105}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 28, "namespace": "view", "name": "shop::products.view.types.configurable", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/configurable.blade.php", "line": 278}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.055525, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:91", "source": {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=91", "ajax": false, "filename": "ConfigurableOption.php", "line": "91"}, "connection": "mlk", "explain": null, "start_percent": 89.289, "width_percent": 0.425}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.0907078, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.index:4", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=4", "ajax": false, "filename": "index.blade.php", "line": "4"}, "connection": "mlk", "explain": null, "start_percent": 89.714, "width_percent": 3.161}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "type": "query", "params": [], "bindings": [1, "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 148}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.094367, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.top:148", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=148", "ajax": false, "filename": "top.blade.php", "line": "148"}, "connection": "mlk", "explain": null, "start_percent": 92.875, "width_percent": 0.362}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 275}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.097195, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.top:275", "source": {"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 275}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=275", "ajax": false, "filename": "top.blade.php", "line": "275"}, "connection": "mlk", "explain": null, "start_percent": 93.237, "width_percent": 0.377}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.11312, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:359", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=359", "ajax": false, "filename": "index.blade.php", "line": "359"}, "connection": "mlk", "explain": null, "start_percent": 93.614, "width_percent": 0.472}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "type": "query", "params": [], "bindings": [1, "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 442}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.114342, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:442", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 442}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=442", "ajax": false, "filename": "index.blade.php", "line": "442"}, "connection": "mlk", "explain": null, "start_percent": 94.086, "width_percent": 0.267}, {"sql": "select * from `theme_customizations` where `type` = 'services_content' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "type": "query", "params": [], "bindings": ["services_content", 1, "default", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "shop::components.layouts.services", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.php", "line": 13}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.117105, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 94.354, "width_percent": 2.878}, {"sql": "select * from `theme_customizations` where `type` = 'footer_links' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "type": "query", "params": [], "bindings": ["footer_links", 1, "default", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "shop::components.layouts.footer.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.php", "line": 17}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.120043, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 97.232, "width_percent": 0.267}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 23, "namespace": "view", "name": "shop::components.layouts.footer.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.1208, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 97.499, "width_percent": 2.501}]}, "models": {"data": {"Webkul\\Product\\Models\\ProductAttributeValue": {"value": 79, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 73, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeOptionTranslation": {"value": 60, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeOptionTranslation.php&line=1", "ajax": false, "filename": "AttributeOptionTranslation.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeTranslation": {"value": 35, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeTranslation.php&line=1", "ajax": false, "filename": "AttributeTranslation.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeOption": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeOption.php&line=1", "ajax": false, "filename": "AttributeOption.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductPriceIndex": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductPriceIndex.php&line=1", "ajax": false, "filename": "ProductPriceIndex.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomizationTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomizationTranslation.php&line=1", "ajax": false, "filename": "ThemeCustomizationTranslation.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventoryIndex": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventoryIndex.php&line=1", "ajax": false, "filename": "ProductInventoryIndex.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomization": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomization.php&line=1", "ajax": false, "filename": "ThemeCustomization.php", "line": "?"}}}, "count": 317, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/iphone-ip1212-pro", "action_name": "shop.product_or_category.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\ProductsCategoriesProxyController@index", "uri": "GET {fallbackPlaceholder}", "controller": "Webkul\\Shop\\Http\\Controllers\\ProductsCategoriesProxyController@index<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FProductsCategoriesProxyController.php&line=37\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FProductsCategoriesProxyController.php&line=37\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/ProductsCategoriesProxyController.php:37-131</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, cache.response", "duration": "464ms", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1751280716 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1751280716\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-156912116 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-156912116\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-387802108 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IkNaRnhyYkZwVUZDR2pUazNORzVsVUE9PSIsInZhbHVlIjoiOE5nVjVFOVRsOWFpSmJ0SjFpV3RpUFExSzhZWmRjcm96U2xIb0RvWjZ6dzRqQjJwckllQlpiVnB2YVdaTkpiUFRtM0ZocXdocTNxajBPQWtuYmJXWitOU2lXdFIrK3J2REpqRkpGVDlmS0Q5endNMEdwcXlTellGUzd3OE8zU00iLCJtYWMiOiI0ODhhNzRiNzI4ZjFhYzQwYTNiMDAwZTcwMTk3MGUyM2I4Nzc0ZTk4Y2RhZmY3YmEyMDE2NWFhYThkMjhiYTQ3IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Im1tdHVjdm4wWFpZeStHd3lzaUZMQmc9PSIsInZhbHVlIjoiUWpPTExZclBTME5HOUZRd0NST1MvNFBiQWFPcXM5QXVKT2hYRTZqMFMwbzVwOTYzL1cwRHJoM3EyYW1xM045RVA5ZzJ0UnJsVDdCam1HWFVpeGZRb2RDeUZZWUc2dUdCdHI2SVVjU0V1eTE4emJNQVZia042R3FwdjJqYXp5VDEiLCJtYWMiOiIzYmRhMGVhZDQ5ZmUwZGQ3N2RjYjExZDI4YTM4NGYyOWEyNmRjM2Y4NWRkM2E1OGJlNzEyNmE4MTk0Nzg3MDgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://mlk.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387802108\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1355054016 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ip_address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vnSyIG6iJ55AHBBUPGgOi0i0oU87v90LJuXfNHRl</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">88qQnIrt45zOEcbMyeOCCgAapTdKA6feHMFNH0Bh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1355054016\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-696659866 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:47:42 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-696659866\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-350151302 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vnSyIG6iJ55AHBBUPGgOi0i0oU87v90LJuXfNHRl</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => {<a class=sf-dump-ref>#1784</a><samp data-depth=2 class=sf-dump-compact>\n    +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">id</span>\": <span class=sf-dump-num>37</span>\n  </samp>}\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-350151302\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/iphone-ip1212-pro", "action_name": "shop.product_or_category.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\ProductsCategoriesProxyController@index"}, "badge": null}}