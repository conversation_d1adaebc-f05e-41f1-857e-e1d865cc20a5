{"__meta": {"id": "01K2Y1YX8TE2B7EXDBFN8W0TTT", "datetime": "2025-08-18 08:40:34", "utime": **********.971149, "method": "GET", "uri": "/cache/original/product/241/Z09omGnqGzZauxZG6t3pJ1kkjhwzt7BDtlK6o3bm.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.782604, "end": **********.981939, "duration": 0.19933509826660156, "duration_str": "199ms", "measures": [{"label": "Booting", "start": **********.782604, "relative_start": 0, "end": **********.952586, "relative_end": **********.952586, "duration": 0.****************, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.952604, "relative_start": 0.****************, "end": **********.981942, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "29.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.963274, "relative_start": 0.*****************, "end": **********.966489, "relative_end": **********.966489, "duration": 0.0032150745391845703, "duration_str": "3.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.969828, "relative_start": 0.****************, "end": **********.969928, "relative_end": **********.969928, "duration": 0.00010013580322265625, "duration_str": "100μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.969942, "relative_start": 0.*****************, "end": **********.969955, "relative_end": **********.969955, "duration": 1.2874603271484375e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/241/Z09omGnqGzZauxZG6t3pJ1kkjhwzt7BDtlK6o3bm.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "200ms", "peak_memory": "38MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1722475977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1722475977\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1440616407 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1440616407\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2040942356 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6InBhVVpuYkd6cVRHZE5ubm1ORGNhcVE9PSIsInZhbHVlIjoiRFJvbG9yMHR2cVlWc1Q3VUl5WTZVUkRPdzhRNmtpUzRJWERFSDI1SHN5b0xZRW1GNSswU2FCUzkwWjI2eXhEK1ZHWm1EYndpV2lFMVhxSkVENnpkREJic3lUbURPQ0U5cEdkVVZyREdDV2EwdkVGa09vNDNsRURFRXhjRkJHZ2kiLCJtYWMiOiJmM2M3ZjcxNmE0ZDQ0YTgyNjQ2ODQ5MmYzZGEzZWI0ZDViNGMyODFiZTIxZmI2MTVmNDBjZWVlNTRmMjJiYzhmIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Ilc0Wk5pODZCZi9HRTg4VlZ1cExXN3c9PSIsInZhbHVlIjoibE1NY3BNMEFGeG1Eb2k4NTU0TWhPdkFwVEprWmFDT0l6bkhNYUFoazB4UGxuMUdSQThKL1VLU3FiM0lTSnJkd1VHWUpGQ3F5Y3RpMzRpOWN6SUUvYURYek5xUUtRTnNkNDc4Q3dURFhPUEpuZlJXTDRNUGI5RzVOcWlwRUNGY1AiLCJtYWMiOiIwZDA5ODAzMGY1ZjQ5YTY0ZWU5ZWI3Y2I0YjA5NWZiNGU0NTYzZWI3Y2VlYjY4MzYwNWRlMzE5ZTAxY2E2MTY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://mlk.test/5641234-variant-4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040942356\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InBhVVpuYkd6cVRHZE5ubm1ORGNhcVE9PSIsInZhbHVlIjoiRFJvbG9yMHR2cVlWc1Q3VUl5WTZVUkRPdzhRNmtpUzRJWERFSDI1SHN5b0xZRW1GNSswU2FCUzkwWjI2eXhEK1ZHWm1EYndpV2lFMVhxSkVENnpkREJic3lUbURPQ0U5cEdkVVZyREdDV2EwdkVGa09vNDNsRURFRXhjRkJHZ2kiLCJtYWMiOiJmM2M3ZjcxNmE0ZDQ0YTgyNjQ2ODQ5MmYzZGEzZWI0ZDViNGMyODFiZTIxZmI2MTVmNDBjZWVlNTRmMjJiYzhmIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilc0Wk5pODZCZi9HRTg4VlZ1cExXN3c9PSIsInZhbHVlIjoibE1NY3BNMEFGeG1Eb2k4NTU0TWhPdkFwVEprWmFDT0l6bkhNYUFoazB4UGxuMUdSQThKL1VLU3FiM0lTSnJkd1VHWUpGQ3F5Y3RpMzRpOWN6SUUvYURYek5xUUtRTnNkNDc4Q3dURFhPUEpuZlJXTDRNUGI5RzVOcWlwRUNGY1AiLCJtYWMiOiIwZDA5ODAzMGY1ZjQ5YTY0ZWU5ZWI3Y2I0YjA5NWZiNGU0NTYzZWI3Y2VlYjY4MzYwNWRlMzE5ZTAxY2E2MTY1IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-50120438 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">60398</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">8546f01c65af44f66cdb8cafb4bc4dc9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:40:34 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-50120438\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1920204376 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1920204376\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/241/Z09omGnqGzZauxZG6t3pJ1kkjhwzt7BDtlK6o3bm.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}