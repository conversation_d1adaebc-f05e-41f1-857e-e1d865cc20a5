{"__meta": {"id": "01K2Y21C6KJWWZ4JXNA17S40SJ", "datetime": "2025-08-18 08:41:55", "utime": **********.795515, "method": "GET", "uri": "/cache/original/product/248/QT6kGRlviVp47spm8LfUzUAaAN4t5shHjWiJBvhP.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.613851, "end": **********.808414, "duration": 0.1945629119873047, "duration_str": "195ms", "measures": [{"label": "Booting", "start": **********.613851, "relative_start": 0, "end": **********.778727, "relative_end": **********.778727, "duration": 0.*****************, "duration_str": "165ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.778738, "relative_start": 0.****************, "end": **********.808416, "relative_end": 1.9073486328125e-06, "duration": 0.029677867889404297, "duration_str": "29.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.788278, "relative_start": 0.*****************, "end": **********.791509, "relative_end": **********.791509, "duration": 0.0032308101654052734, "duration_str": "3.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.793912, "relative_start": 0.*****************, "end": **********.794, "relative_end": **********.794, "duration": 8.797645568847656e-05, "duration_str": "88μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.794012, "relative_start": 0.****************, "end": **********.794023, "relative_end": **********.794023, "duration": 1.0967254638671875e-05, "duration_str": "11μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/248/QT6kGRlviVp47spm8LfUzUAaAN4t5shHjWiJBvhP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "196ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1582191486 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1582191486\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-920954544 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-920954544\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1908898738 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6InVWNmNzbmRLSWtSUk9EUGQ5OHhCT1E9PSIsInZhbHVlIjoiNWJQM25aQ2JNRFlOL0p5eFBYZWIza1BqT0VIL1dVamtzVis5NjlhU2cwcjkyNXVLVGd4eEhNRkdMMlhmVEZRYlBCYmJmckJEZCtTaVovNHNEUzhkTGI1ZU4xZS9kSkpxdk1WVi83TG9zdjNtZmwrOG9udjNYZmk3bXJOV2RsVFQiLCJtYWMiOiIwNzM0OTQwZDJiNTRjODYyZTdiNTdhN2EyYTFkNmM5NmJmYTM2YjVlNjllY2RmZTc2NDlhNjgxNDU0MWZjNTE4IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IjJGcVpxVnUwY3ZLOXo5YVFPRjVyL1E9PSIsInZhbHVlIjoiQXNkbWN5cTM1WVJMcXRQVTB5WEppN2l5d1YxYTJ1OTJaNFZybWtvWEY3TzJrVVRybWlScFU5SkFhNnhzcEdnNG9yYVlQT1JXVVJQZ25CNkFIVDdwaWVFL3ArZXZEWnQzMStqcFZhRXdaQVI4dXlINzJtRU9TWktLekhRY3JwSUYiLCJtYWMiOiI5NmZhOGI0ODQzMDdmOWQyYWI1Yzk2YTA3ZjZmZTg4MzNkMTczOWVkNmUxMWEwYmEzZjIyYzJlNzY0MjRlZjc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://mlk.test/iphone-ip1212-pro</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908898738\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1821294643 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InVWNmNzbmRLSWtSUk9EUGQ5OHhCT1E9PSIsInZhbHVlIjoiNWJQM25aQ2JNRFlOL0p5eFBYZWIza1BqT0VIL1dVamtzVis5NjlhU2cwcjkyNXVLVGd4eEhNRkdMMlhmVEZRYlBCYmJmckJEZCtTaVovNHNEUzhkTGI1ZU4xZS9kSkpxdk1WVi83TG9zdjNtZmwrOG9udjNYZmk3bXJOV2RsVFQiLCJtYWMiOiIwNzM0OTQwZDJiNTRjODYyZTdiNTdhN2EyYTFkNmM5NmJmYTM2YjVlNjllY2RmZTc2NDlhNjgxNDU0MWZjNTE4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjJGcVpxVnUwY3ZLOXo5YVFPRjVyL1E9PSIsInZhbHVlIjoiQXNkbWN5cTM1WVJMcXRQVTB5WEppN2l5d1YxYTJ1OTJaNFZybWtvWEY3TzJrVVRybWlScFU5SkFhNnhzcEdnNG9yYVlQT1JXVVJQZ25CNkFIVDdwaWVFL3ArZXZEWnQzMStqcFZhRXdaQVI4dXlINzJtRU9TWktLekhRY3JwSUYiLCJtYWMiOiI5NmZhOGI0ODQzMDdmOWQyYWI1Yzk2YTA3ZjZmZTg4MzNkMTczOWVkNmUxMWEwYmEzZjIyYzJlNzY0MjRlZjc0IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821294643\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2091139044 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">51050</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">60f5af4014d20ce838dd3e4f022f2535</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:41:55 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091139044\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1879524349 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1879524349\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/248/QT6kGRlviVp47spm8LfUzUAaAN4t5shHjWiJBvhP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}