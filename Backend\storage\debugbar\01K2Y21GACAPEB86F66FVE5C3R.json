{"__meta": {"id": "01K2Y21GACAPEB86F66FVE5C3R", "datetime": "2025-08-18 08:42:00", "utime": **********.012584, "method": "GET", "uri": "/cache/original/product/244/ZCqqQHZxwXJvAvzNecIbDxNS9hVIgSFDWqpojKPn.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.830318, "end": **********.022727, "duration": 0.19240903854370117, "duration_str": "192ms", "measures": [{"label": "Booting", "start": **********.830318, "relative_start": 0, "end": **********.99504, "relative_end": **********.99504, "duration": 0.*****************, "duration_str": "165ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.995051, "relative_start": 0.****************, "end": **********.022731, "relative_end": 4.0531158447265625e-06, "duration": 0.027680158615112305, "duration_str": "27.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.00541, "relative_start": 0.*****************, "end": **********.008811, "relative_end": **********.008811, "duration": 0.003401041030883789, "duration_str": "3.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.011223, "relative_start": 0.*****************, "end": **********.011322, "relative_end": **********.011322, "duration": 9.894371032714844e-05, "duration_str": "99μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.011336, "relative_start": 0.*****************, "end": **********.01135, "relative_end": **********.01135, "duration": 1.3828277587890625e-05, "duration_str": "14μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/244/ZCqqQHZxwXJvAvzNecIbDxNS9hVIgSFDWqpojKPn.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "193ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-586569829 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-586569829\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-683252951 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-683252951\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1632113730 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IkNsSTNPRHBpcG0rc1VVRmZJZ0UxM0E9PSIsInZhbHVlIjoiRUREVXNwRjF0VU9mZVNqUkJ4dUF5b3Y2ZG9mTVJSS1ptZ044RVNjeVdnc1JNeW53SFdac3ltSVFrUkJvcEYwRlI4S3VvSEFCQXhrZStEYmZJclVkclgvN1ptTkdaUTl4NWJNdGlRQnNmZk5xVSthUGVDUkhjT0Iwd0loZmpPd0wiLCJtYWMiOiI4ZDc2YTQ3ZGZjNGQ4NzI3NzAyZmFjYjUwZTdlMjM1MDIwNDc5MTMzMjdjYWZhN2YwNjA5OTllOTFkODI4YWNhIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IjFjdDJ2UlpLcFVYZ3hWVlZtb043aWc9PSIsInZhbHVlIjoickg2ODBsNHNxNjl2T29IQmEzRDZhME40WWRsbDBpbSs1RUk3YlYxQWVEdm9peGV4VW5mZVFNSy9BU2MvRjA0MUlTTHJSRDdXSEt5dFVTODkyWFc2R2g3NTR6aEdSclgzKzlsM08vKyt1Tkt5ejBhalNxNUsyU2hlUmhJeTA0UHoiLCJtYWMiOiJjYzY1MzEzMzMzMTA5ZmMzNGIzMmIzNmE2MzIwMDBiNjAzNzZlNDVhMDhhY2YxY2ZhMDcyNzY4ZTE5MTdjMzc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-C<PERSON>,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://mlk.test/iphone-ip1314</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632113730\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1069598085 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkNsSTNPRHBpcG0rc1VVRmZJZ0UxM0E9PSIsInZhbHVlIjoiRUREVXNwRjF0VU9mZVNqUkJ4dUF5b3Y2ZG9mTVJSS1ptZ044RVNjeVdnc1JNeW53SFdac3ltSVFrUkJvcEYwRlI4S3VvSEFCQXhrZStEYmZJclVkclgvN1ptTkdaUTl4NWJNdGlRQnNmZk5xVSthUGVDUkhjT0Iwd0loZmpPd0wiLCJtYWMiOiI4ZDc2YTQ3ZGZjNGQ4NzI3NzAyZmFjYjUwZTdlMjM1MDIwNDc5MTMzMjdjYWZhN2YwNjA5OTllOTFkODI4YWNhIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjFjdDJ2UlpLcFVYZ3hWVlZtb043aWc9PSIsInZhbHVlIjoickg2ODBsNHNxNjl2T29IQmEzRDZhME40WWRsbDBpbSs1RUk3YlYxQWVEdm9peGV4VW5mZVFNSy9BU2MvRjA0MUlTTHJSRDdXSEt5dFVTODkyWFc2R2g3NTR6aEdSclgzKzlsM08vKyt1Tkt5ejBhalNxNUsyU2hlUmhJeTA0UHoiLCJtYWMiOiJjYzY1MzEzMzMzMTA5ZmMzNGIzMmIzNmE2MzIwMDBiNjAzNzZlNDVhMDhhY2YxY2ZhMDcyNzY4ZTE5MTdjMzc3IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1069598085\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-48224345 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">33884</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">8ac8c86c8c2fbef531bd3738bf549939</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:42:00 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-48224345\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1349624534 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1349624534\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/244/ZCqqQHZxwXJvAvzNecIbDxNS9hVIgSFDWqpojKPn.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}