{"__meta": {"id": "01K2Y10B2RSXFSSVMQWB3VXEW7", "datetime": "2025-08-18 08:23:53", "utime": **********.304857, "method": "GET", "uri": "/cache/large/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.129448, "end": **********.315597, "duration": 0.18614912033081055, "duration_str": "186ms", "measures": [{"label": "Booting", "start": **********.129448, "relative_start": 0, "end": **********.286148, "relative_end": **********.286148, "duration": 0.*****************, "duration_str": "157ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.286158, "relative_start": 0.*****************, "end": **********.315599, "relative_end": 1.9073486328125e-06, "duration": 0.029440879821777344, "duration_str": "29.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.295695, "relative_start": 0.*****************, "end": **********.298911, "relative_end": **********.298911, "duration": 0.0032160282135009766, "duration_str": "3.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.303228, "relative_start": 0.*****************, "end": **********.303317, "relative_end": **********.303317, "duration": 8.916854858398438e-05, "duration_str": "89μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.303328, "relative_start": 0.*****************, "end": **********.303339, "relative_end": **********.303339, "duration": 1.0967254638671875e-05, "duration_str": "11μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "187ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1294457913 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1294457913\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1626691915 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1626691915\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1585320486 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6InV5Z3NaRFhzMVRMb29EUC8zL043Vnc9PSIsInZhbHVlIjoid0RQbW5YOGkrM2U2TWhJcWtoektvUWNKMUUvTVFjMFF0TG5DMlN6TUZoc0ZJb1FlYTFtRkI2TjFjWmYvd3NVZS9JUmczdzYyUGF5RVEya2ZaRGV4bTR4dTZSeVRacFNBRHg4STJtRjNNVjV3ZndmMHVrSUxjeHpubG5FN0V1bGkiLCJtYWMiOiI5Y2Y3ZjhmYTc1OWI4M2UxNTE5OGJmZTEyN2ZiNTRkNWYzYTMyMWNmOTg3ZDY1MDJiNThhYjdjZjU2MWI3OTFjIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Ik9OanBHMzR4SkNXUE96MnBFSTZ0c2c9PSIsInZhbHVlIjoienlzOE82NTdzdjdhMk9KaUdNL09TakQ4ME9vWmRBL290c2RlaHVQclp4OHk4U2daT2poZEFOSldyeHQ4aDM0VmkweEpMMXcydklldWFuOGNpNTNvVHYrM1NkMnlHei8ybVhPajQ5UkxOdXdNSSsxSlV4MWNObEFSb2dBNFFUcngiLCJtYWMiOiI5YjBhYjA3NWY1ZjU5NjBmZjJkZGY2NWU2MDQwNGIxZDhjMDE1ZDE0MmIwMzdlNGM4MTVmMTQ0MDc0ZWU0NDQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-C<PERSON>,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://mlk.test/1010-iphone-ip14-pro-max-67</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1585320486\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1635148398 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InV5Z3NaRFhzMVRMb29EUC8zL043Vnc9PSIsInZhbHVlIjoid0RQbW5YOGkrM2U2TWhJcWtoektvUWNKMUUvTVFjMFF0TG5DMlN6TUZoc0ZJb1FlYTFtRkI2TjFjWmYvd3NVZS9JUmczdzYyUGF5RVEya2ZaRGV4bTR4dTZSeVRacFNBRHg4STJtRjNNVjV3ZndmMHVrSUxjeHpubG5FN0V1bGkiLCJtYWMiOiI5Y2Y3ZjhmYTc1OWI4M2UxNTE5OGJmZTEyN2ZiNTRkNWYzYTMyMWNmOTg3ZDY1MDJiNThhYjdjZjU2MWI3OTFjIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9OanBHMzR4SkNXUE96MnBFSTZ0c2c9PSIsInZhbHVlIjoienlzOE82NTdzdjdhMk9KaUdNL09TakQ4ME9vWmRBL290c2RlaHVQclp4OHk4U2daT2poZEFOSldyeHQ4aDM0VmkweEpMMXcydklldWFuOGNpNTNvVHYrM1NkMnlHei8ybVhPajQ5UkxOdXdNSSsxSlV4MWNObEFSb2dBNFFUcngiLCJtYWMiOiI5YjBhYjA3NWY1ZjU5NjBmZjJkZGY2NWU2MDQwNGIxZDhjMDE1ZDE0MmIwMzdlNGM4MTVmMTQ0MDc0ZWU0NDQ2IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1635148398\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1268695281 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">12710</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">357e248373dcd00e07e4a9d77c95c9db</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:23:53 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268695281\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-831353629 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-831353629\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}