{"__meta": {"id": "01K2Y103MRK850DWMGHKPX7KWF", "datetime": "2025-08-18 08:23:45", "utime": **********.689093, "method": "GET", "uri": "/cache/large/product/226/RDQz93uMZfPTmiQjgWWzS8n19sRp2GKvLHQgFmov.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.516009, "end": **********.698263, "duration": 0.18225383758544922, "duration_str": "182ms", "measures": [{"label": "Booting", "start": **********.516009, "relative_start": 0, "end": **********.669607, "relative_end": **********.669607, "duration": 0.*****************, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.669618, "relative_start": 0.****************, "end": **********.698266, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "28.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.680018, "relative_start": 0.*****************, "end": **********.683243, "relative_end": **********.683243, "duration": 0.003225088119506836, "duration_str": "3.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.687478, "relative_start": 0.*****************, "end": **********.68759, "relative_end": **********.68759, "duration": 0.00011181831359863281, "duration_str": "112μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.687607, "relative_start": 0.*****************, "end": **********.687625, "relative_end": **********.687625, "duration": 1.7881393432617188e-05, "duration_str": "18μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/product/226/RDQz93uMZfPTmiQjgWWzS8n19sRp2GKvLHQgFmov.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "183ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1215103330 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1215103330\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-517390408 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-517390408\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-440831673 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6ImNXT3BRY21nSjZjYUp0Qmh4Vkk4Unc9PSIsInZhbHVlIjoic3Jqa3RuWTgxU0dIZ3o2TWNzM1kweTkvKzMraGZVdUZuOGo2dUpHZk53a25KbXZFeTlVR3V5bUxqcXlvLzM3Z3JYZzBTUGIwZFdlYzVtbVkrVlhLWnpLRGJMRlhMNnVOL0JkRkNIWHZFakRqczAycmRGQzdzUEFaemhrMkVENWMiLCJtYWMiOiJhYWJhOTAwMGY0NDk2Mzk4NDc0MDc2ZjlhMDhiNWJiYWEzZDgzZDAxMzBhNGE2ZDYyMmI5YzVmZGMwNjVlYTIwIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IjlTMTh2RjROZ3I5cTBMMHh1SEt4WUE9PSIsInZhbHVlIjoiaVNQc3IvTzhMY3hjclh0WEJpQW45T1dlYTZlOFl0UG5Pa3VaY1V2am5KOGI4NzF1YTM4UC9ReExqQlMybTNyYzlzVVNzcHowWEt2RkMrdnRJTVkyZGRGcTlTOXA2NE1FQSs3RUZrQjBzdkdnN1RZNWpUYUpTKzJHNVozWnU3alUiLCJtYWMiOiIxNWU2ZmFmOTE5NDljYmE0OTI5M2FmMzE5YjdjMGFmMDUzM2E1YjFhNTdkNGI3NjQ0NDRjNDgyYmMzMDcyOGRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://mlk.test/1022-iphone-ip15-pro-max</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-440831673\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-540117608 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImNXT3BRY21nSjZjYUp0Qmh4Vkk4Unc9PSIsInZhbHVlIjoic3Jqa3RuWTgxU0dIZ3o2TWNzM1kweTkvKzMraGZVdUZuOGo2dUpHZk53a25KbXZFeTlVR3V5bUxqcXlvLzM3Z3JYZzBTUGIwZFdlYzVtbVkrVlhLWnpLRGJMRlhMNnVOL0JkRkNIWHZFakRqczAycmRGQzdzUEFaemhrMkVENWMiLCJtYWMiOiJhYWJhOTAwMGY0NDk2Mzk4NDc0MDc2ZjlhMDhiNWJiYWEzZDgzZDAxMzBhNGE2ZDYyMmI5YzVmZGMwNjVlYTIwIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjlTMTh2RjROZ3I5cTBMMHh1SEt4WUE9PSIsInZhbHVlIjoiaVNQc3IvTzhMY3hjclh0WEJpQW45T1dlYTZlOFl0UG5Pa3VaY1V2am5KOGI4NzF1YTM4UC9ReExqQlMybTNyYzlzVVNzcHowWEt2RkMrdnRJTVkyZGRGcTlTOXA2NE1FQSs3RUZrQjBzdkdnN1RZNWpUYUpTKzJHNVozWnU3alUiLCJtYWMiOiIxNWU2ZmFmOTE5NDljYmE0OTI5M2FmMzE5YjdjMGFmMDUzM2E1YjFhNTdkNGI3NjQ0NDRjNDgyYmMzMDcyOGRjIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540117608\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2127116769 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">14388</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">fb4b82fc196c4fe5c64df4c966804052</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:23:45 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2127116769\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1064099752 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1064099752\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/product/226/RDQz93uMZfPTmiQjgWWzS8n19sRp2GKvLHQgFmov.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}