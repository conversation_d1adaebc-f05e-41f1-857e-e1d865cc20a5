{"__meta": {"id": "01K2Y10AJHZXK8WQ99BGH5NWG1", "datetime": "2025-08-18 08:23:52", "utime": **********.78622, "method": "GET", "uri": "/cache/small/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.59063, "end": **********.796485, "duration": 0.2058548927307129, "duration_str": "206ms", "measures": [{"label": "Booting", "start": **********.59063, "relative_start": 0, "end": **********.765366, "relative_end": **********.765366, "duration": 0.*****************, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.765378, "relative_start": 0.*****************, "end": **********.796487, "relative_end": 2.1457672119140625e-06, "duration": 0.031109094619750977, "duration_str": "31.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.777032, "relative_start": 0.****************, "end": **********.780455, "relative_end": **********.780455, "duration": 0.0034232139587402344, "duration_str": "3.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.784787, "relative_start": 0.*****************, "end": **********.78489, "relative_end": **********.78489, "duration": 0.000102996826171875, "duration_str": "103μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.784904, "relative_start": 0.****************, "end": **********.784915, "relative_end": **********.784915, "duration": 1.0967254638671875e-05, "duration_str": "11μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/small/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "206ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1631460038 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1631460038\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-430812226 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-430812226\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1976602372 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IjlXSmxGa3lQSTRTalplVno1YmJUV3c9PSIsInZhbHVlIjoiS3k3M1F4bERyTmE2bUhtTWtYUDZEMDdDRStDYjZTZmtRRkR0RGhIdTV1Vy84Zk1YaVFoUklPdXdIUTN6ZEcwOUx0TkpuNGs5bFlWZnFVRHNqdnpMSnhaN3NvY2R6VTc3d096NkRJbStWcnJEcFpRdGxOZ2h0MHdjY3pKRWZwK0wiLCJtYWMiOiI0NzdmN2UxZmEzYzJlZmE1Y2QyNTg5MWQ1ZmE1NGRlNjZlMDk2ZGNmMmQxMmFiMmEzYThhNDAyNTk0MGM3ZjEzIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Ijhka1hUbFFUT2plU3NqYmtJTkRFNHc9PSIsInZhbHVlIjoiYmV5eWs0ZTc0MGpEMjZvNEtqUVhBVURGYy9TOS80cXhneGxLZldOYytnYnc5NitVZCt1ODB2V1lXVFo5L1cvamx6V1lwTkdjZWlCWHJYbVBZVUllMml6Vkg4Z01GRk43TGVHZFBOZGorRmlXUXZvTG9tYzFxSmxNU044Z1dtQlgiLCJtYWMiOiJjYjc1MGI4NTg5MTVkNjhkNGJmODgxZGQxNjllZTJhYmIyODJhZjMwNzU4YzM4Yzc1NTdhNTYwZDlhMTRlZDljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-C<PERSON>,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://mlk.test/1010-iphone-ip14-pro-max-67</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976602372\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-719812884 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjlXSmxGa3lQSTRTalplVno1YmJUV3c9PSIsInZhbHVlIjoiS3k3M1F4bERyTmE2bUhtTWtYUDZEMDdDRStDYjZTZmtRRkR0RGhIdTV1Vy84Zk1YaVFoUklPdXdIUTN6ZEcwOUx0TkpuNGs5bFlWZnFVRHNqdnpMSnhaN3NvY2R6VTc3d096NkRJbStWcnJEcFpRdGxOZ2h0MHdjY3pKRWZwK0wiLCJtYWMiOiI0NzdmN2UxZmEzYzJlZmE1Y2QyNTg5MWQ1ZmE1NGRlNjZlMDk2ZGNmMmQxMmFiMmEzYThhNDAyNTk0MGM3ZjEzIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ijhka1hUbFFUT2plU3NqYmtJTkRFNHc9PSIsInZhbHVlIjoiYmV5eWs0ZTc0MGpEMjZvNEtqUVhBVURGYy9TOS80cXhneGxLZldOYytnYnc5NitVZCt1ODB2V1lXVFo5L1cvamx6V1lwTkdjZWlCWHJYbVBZVUllMml6Vkg4Z01GRk43TGVHZFBOZGorRmlXUXZvTG9tYzFxSmxNU044Z1dtQlgiLCJtYWMiOiJjYjc1MGI4NTg5MTVkNjhkNGJmODgxZGQxNjllZTJhYmIyODJhZjMwNzU4YzM4Yzc1NTdhNTYwZDlhMTRlZDljIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-719812884\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1866370033 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1710</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">1ebaccd5db7f532bb9101b37b71445fb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:23:52 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1866370033\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1013156606 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1013156606\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/small/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}