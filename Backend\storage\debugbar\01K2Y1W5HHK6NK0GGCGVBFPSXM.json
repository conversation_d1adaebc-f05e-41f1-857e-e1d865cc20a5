{"__meta": {"id": "01K2Y1W5HHK6NK0GGCGVBFPSXM", "datetime": "2025-08-18 08:39:05", "utime": **********.137889, "method": "GET", "uri": "/api/products?sort=price-asc&limit=10&price=0", "ip": "127.0.0.1"}, "modules": {"count": 5, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (37)", "Webkul\\Attribute\\Models\\AttributeFamily (2)"], "views": [], "queries": [{"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('sort', 'limit', 'price', 'channel_id', 'status', 'visible_individually', 'url_key')", "duration": 2.3, "duration_str": "2.3s", "connection": "mlk"}, {"sql": "select * from `attributes` where `code` = 'price'", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (2)", "duration": 1.78, "duration_str": "1.78s", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (2)", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "duration": 4.62, "duration_str": "4.62s", "connection": "mlk"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (5)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 13.31, "duration_str": "13.31s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 2", "duration": 2.05, "duration_str": "2.05s", "connection": "mlk"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (1)"], "views": [], "queries": [{"sql": "select * from `customer_groups` where `code` = 'guest'", "duration": 1.97, "duration_str": "1.97s", "connection": "mlk"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (34)", "Webkul\\Product\\Models\\ProductImage (18)", "Webkul\\Product\\Models\\ProductAttributeValue (812)", "Webkul\\Product\\Models\\ProductPriceIndex (102)", "Webkul\\Product\\Models\\ProductInventoryIndex (34)"], "views": [], "queries": [{"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id` order by `product_price_indices`.`min_price` asc limit 10 offset 0", "duration": 10.59, "duration_str": "10.59s", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243) order by `position` asc", "duration": 1.94, "duration_str": "1.94s", "connection": "mlk"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243) order by `position` asc", "duration": 2.43, "duration_str": "2.43s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243)", "duration": 1.52, "duration_str": "1.52s", "connection": "mlk"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243)", "duration": 0.35, "duration_str": "350ms", "connection": "mlk"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243)", "duration": 1.84, "duration_str": "1.84s", "connection": "mlk"}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243)", "duration": 1.92, "duration_str": "1.92s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243)", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (196, 197, 212, 213, 214, 215, 216, 218, 220, 221, 222, 223, 224, 225, 227, 228, 229, 231, 232, 233, 240, 241, 242, 243)", "duration": 1.24, "duration_str": "1.24s", "connection": "mlk"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (196, 197, 212, 213, 214, 215, 216, 218, 220, 221, 222, 223, 224, 225, 227, 228, 229, 231, 232, 233, 240, 241, 242, 243)", "duration": 0.39, "duration_str": "390ms", "connection": "mlk"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (196, 197, 212, 213, 214, 215, 216, 218, 220, 221, 222, 223, 224, 225, 227, 228, 229, 231, 232, 233, 240, 241, 242, 243)", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": [], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}]}]}, "messages": {"count": 20, "messages": [{"message": "[08:39:05] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.045458, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.045551, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.045611, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.045665, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.045717, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.045767, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.045815, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.045863, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.045911, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.04596, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.07177, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.078686, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.085638, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.092236, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.098525, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.105351, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.112466, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.120371, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.127238, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:05] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.134197, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.718857, "end": **********.144913, "duration": 0.426055908203125, "duration_str": "426ms", "measures": [{"label": "Booting", "start": **********.718857, "relative_start": 0, "end": **********.90145, "relative_end": **********.90145, "duration": 0.*****************, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.901462, "relative_start": 0.*****************, "end": **********.144915, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "243ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.912757, "relative_start": 0.*****************, "end": **********.915996, "relative_end": **********.915996, "duration": 0.003239154815673828, "duration_str": "3.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.046073, "relative_start": 0.*****************, "end": **********.136761, "relative_end": **********.136761, "duration": 0.*****************, "duration_str": "90.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::products.prices.configurable", "start": **********.070978, "relative_start": 0.*****************, "end": **********.070978, "relative_end": **********.070978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.configurable", "start": **********.078476, "relative_start": 0.********22064209, "end": **********.078476, "relative_end": **********.078476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.configurable", "start": **********.085291, "relative_start": 0.36643385887145996, "end": **********.085291, "relative_end": **********.085291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.configurable", "start": **********.092023, "relative_start": 0.3731658458709717, "end": **********.092023, "relative_end": **********.092023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.configurable", "start": **********.098278, "relative_start": 0.3794209957122803, "end": **********.098278, "relative_end": **********.098278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.configurable", "start": **********.105127, "relative_start": 0.38627004623413086, "end": **********.105127, "relative_end": **********.105127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.112215, "relative_start": 0.3933579921722412, "end": **********.112215, "relative_end": **********.112215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.12014, "relative_start": 0.40128302574157715, "end": **********.12014, "relative_end": **********.12014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.127025, "relative_start": 0.40816783905029297, "end": **********.127025, "relative_end": **********.127025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.133985, "relative_start": 0.4151279926300049, "end": **********.133985, "relative_end": **********.133985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 44460184, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 10, "nb_templates": 10, "templates": [{"name": "shop::products.prices.configurable", "param_count": null, "params": [], "start": **********.07096, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/prices/configurable.blade.phpshop::products.prices.configurable", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fconfigurable.blade.php&line=1", "ajax": false, "filename": "configurable.blade.php", "line": "?"}}, {"name": "shop::products.prices.configurable", "param_count": null, "params": [], "start": **********.078462, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/prices/configurable.blade.phpshop::products.prices.configurable", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fconfigurable.blade.php&line=1", "ajax": false, "filename": "configurable.blade.php", "line": "?"}}, {"name": "shop::products.prices.configurable", "param_count": null, "params": [], "start": **********.08527, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/prices/configurable.blade.phpshop::products.prices.configurable", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fconfigurable.blade.php&line=1", "ajax": false, "filename": "configurable.blade.php", "line": "?"}}, {"name": "shop::products.prices.configurable", "param_count": null, "params": [], "start": **********.092009, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/prices/configurable.blade.phpshop::products.prices.configurable", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fconfigurable.blade.php&line=1", "ajax": false, "filename": "configurable.blade.php", "line": "?"}}, {"name": "shop::products.prices.configurable", "param_count": null, "params": [], "start": **********.098264, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/prices/configurable.blade.phpshop::products.prices.configurable", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fconfigurable.blade.php&line=1", "ajax": false, "filename": "configurable.blade.php", "line": "?"}}, {"name": "shop::products.prices.configurable", "param_count": null, "params": [], "start": **********.105113, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/prices/configurable.blade.phpshop::products.prices.configurable", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fconfigurable.blade.php&line=1", "ajax": false, "filename": "configurable.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.112204, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.120123, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.127012, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.133972, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "queries": {"count": 26, "nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07795999999999997, "accumulated_duration_str": "77.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "theme", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.931739, "duration": 0.01331, "duration_str": "13.31ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 17.073}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.948391, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 17.073, "width_percent": 0.385}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.950167, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 17.458, "width_percent": 0.321}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.954949, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 17.778, "width_percent": 0.308}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.955674, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 18.086, "width_percent": 0.192}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.956368, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 18.279, "width_percent": 0.308}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.957122, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 18.586, "width_percent": 0.205}, {"sql": "select * from `customer_groups` where `code` = 'guest'", "type": "query", "params": [], "bindings": ["guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 746}, {"index": 19, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}], "start": **********.962384, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 18.792, "width_percent": 2.527}, {"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('sort', 'limit', 'price', 'channel_id', 'status', 'visible_individually', 'url_key')", "type": "query", "params": [], "bindings": ["sort", "limit", "price", "channel_id", "status", "visible_individually", "url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 175}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 292}, {"index": 18, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 485}], "start": **********.965098, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 21.319, "width_percent": 2.95}, {"sql": "select * from `attributes` where `code` = 'price'", "type": "query", "params": [], "bindings": ["price"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 383}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}], "start": **********.969207, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 24.269, "width_percent": 0.359}, {"sql": "select count(*) as aggregate from (select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": [1, "1", 3, 7, 1, 8, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.9703782, "duration": 0.02734, "duration_str": "27.34ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 24.628, "width_percent": 35.069}, {"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id` order by `product_price_indices`.`min_price` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, "1", 3, 7, 1, 8, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.9984639, "duration": 0.01059, "duration_str": "10.59ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 59.697, "width_percent": 13.584}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.010463, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 73.281, "width_percent": 2.283}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.014373, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 75.564, "width_percent": 2.488}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.017343, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 78.053, "width_percent": 3.117}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.0207121, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 81.17, "width_percent": 1.95}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.025944, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 83.12, "width_percent": 0.449}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.02733, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 83.568, "width_percent": 2.36}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.030216, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 85.929, "width_percent": 2.463}, {"sql": "select * from `products` where `products`.`parent_id` in (195, 211, 219, 226, 230, 239, 240, 241, 242, 243)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.0327492, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 88.391, "width_percent": 0.372}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 27, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 30, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.033742, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 88.763, "width_percent": 0.205}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (196, 197, 212, 213, 214, 215, 216, 218, 220, 221, 222, 223, 224, 225, 227, 228, 229, 231, 232, 233, 240, 241, 242, 243)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 27, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 30, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.034496, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 88.969, "width_percent": 1.591}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (196, 197, 212, 213, 214, 215, 216, 218, 220, 221, 222, 223, 224, 225, 227, 228, 229, 231, 232, 233, 240, 241, 242, 243)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 27, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 30, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.039542, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 90.559, "width_percent": 0.5}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (196, 197, 212, 213, 214, 215, 216, 218, 220, 221, 222, 223, 224, 225, 227, 228, 229, 231, 232, 233, 240, 241, 242, 243)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 27, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 30, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.04106, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 91.06, "width_percent": 0.385}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 190}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}], "start": **********.0497398, "duration": 0.00462, "duration_str": "4.62ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mlk", "explain": null, "start_percent": 91.444, "width_percent": 5.926}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 457}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 479}], "start": **********.06708, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 97.37, "width_percent": 2.63}]}, "models": {"data": {"Webkul\\Product\\Models\\ProductAttributeValue": {"value": 812, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductPriceIndex": {"value": 102, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductPriceIndex.php&line=1", "ajax": false, "filename": "ProductPriceIndex.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 37, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 34, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventoryIndex": {"value": 34, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventoryIndex.php&line=1", "ajax": false, "filename": "ProductInventoryIndex.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}}, "count": 1047, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/api/products?limit=10&price=0&sort=price-asc", "action_name": "shop.api.products.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index", "uri": "GET api/products", "controller": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=27\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/products", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=27\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php:27-56</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance", "duration": "428ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-385146141 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"9 characters\">price-asc</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-385146141\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1606900856 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1606900856\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2015946479 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6InB5bTVKN054NHZJSnRBZndpZWVPTmc9PSIsInZhbHVlIjoiZ1Z5QXNxTE5Wa25TanNvZFYycmo2cEM4R01zQVZCNTdsRzEvVHkyRlhaaFJKcUhwZzNrbllVRmtvaFo1NkVQTkxFelhaVGVFZ21KWHplT2h3Z3VMbGFjL2VWSHVYRXVkZXZJUUhsck5vRjRzUnFCYjJiOHZoU3V0UHEyUCszQlgiLCJtYWMiOiJlMWFjMmFmNGJmZGNlMWYwNTBhMzkyODhiZTg5YzA4NjI4OTQ4NzYzZTQ2NjgyNWY0ZWI1ODBhOWYxZGNhNmIyIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6InlxTmxWS1Z0QnBtSllQcjYxZ2VaM1E9PSIsInZhbHVlIjoiaTVUZm9hSlM2R3lWdEZJWU5HSVZYbVdvTnBNUG5RbjZLU0EvWGszcEVNSTRBbW10SzZyUzVQd2YzTHk5SmRteURNNUtCQTlhUnZIRnFTU2hldFkxRUVXUjUvYzVIMEtuMFdTclZhbVVnNmEyNnpMM0Rxa2RkN3NycmZ4QTJJa1ciLCJtYWMiOiJhOTljMGVmODQxY2IzNTA4YmFkNmEzMDU1YWE3NzNiZGRhYzkyNzJkYmQ2ZTg5M2JkNjg0MzhlYzMwOWU1NTI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://mlk.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InB5bTVKN054NHZJSnRBZndpZWVPTmc9PSIsInZhbHVlIjoiZ1Z5QXNxTE5Wa25TanNvZFYycmo2cEM4R01zQVZCNTdsRzEvVHkyRlhaaFJKcUhwZzNrbllVRmtvaFo1NkVQTkxFelhaVGVFZ21KWHplT2h3Z3VMbGFjL2VWSHVYRXVkZXZJUUhsck5vRjRzUnFCYjJiOHZoU3V0UHEyUCszQlgiLCJtYWMiOiJlMWFjMmFmNGJmZGNlMWYwNTBhMzkyODhiZTg5YzA4NjI4OTQ4NzYzZTQ2NjgyNWY0ZWI1ODBhOWYxZGNhNmIyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2015946479\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1306662370 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ip_address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vnSyIG6iJ55AHBBUPGgOi0i0oU87v90LJuXfNHRl</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">88qQnIrt45zOEcbMyeOCCgAapTdKA6feHMFNH0Bh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306662370\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-302742362 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:39:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-302742362\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-832992770 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vnSyIG6iJ55AHBBUPGgOi0i0oU87v90LJuXfNHRl</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => {<a class=sf-dump-ref>#1708</a><samp data-depth=2 class=sf-dump-compact>\n    +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">id</span>\": <span class=sf-dump-num>37</span>\n  </samp>}\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-832992770\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/api/products?limit=10&price=0&sort=price-asc", "action_name": "shop.api.products.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index"}, "badge": null}}