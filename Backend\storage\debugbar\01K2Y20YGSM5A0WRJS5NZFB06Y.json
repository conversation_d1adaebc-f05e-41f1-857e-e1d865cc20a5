{"__meta": {"id": "01K2Y20YGSM5A0WRJS5NZFB06Y", "datetime": "2025-08-18 08:41:41", "utime": **********.786272, "method": "GET", "uri": "/cache/original/product/243/AhBbSpkRhfGYo92QTqXBT9guHQqTl4aap68KZLYo.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.601675, "end": **********.794934, "duration": 0.19325900077819824, "duration_str": "193ms", "measures": [{"label": "Booting", "start": **********.601675, "relative_start": 0, "end": **********.769541, "relative_end": **********.769541, "duration": 0.*****************, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.769552, "relative_start": 0.****************, "end": **********.794936, "relative_end": 1.9073486328125e-06, "duration": 0.025383949279785156, "duration_str": "25.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.778652, "relative_start": 0.*****************, "end": **********.781871, "relative_end": **********.781871, "duration": 0.003219127655029297, "duration_str": "3.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.785046, "relative_start": 0.*****************, "end": **********.785132, "relative_end": **********.785132, "duration": 8.58306884765625e-05, "duration_str": "86μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.785144, "relative_start": 0.*****************, "end": **********.785154, "relative_end": **********.785154, "duration": 1.0013580322265625e-05, "duration_str": "10μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/243/AhBbSpkRhfGYo92QTqXBT9guHQqTl4aap68KZLYo.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "194ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-687502580 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-687502580\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-972529485 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-972529485\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1264877447 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IjJzTWowQmlYZ0pKeDFTbkoxSTZGVlE9PSIsInZhbHVlIjoiVHFSVC9laWtOZHdWTzhHRUNiTk1NQWs2RzkwbEUxcEM1ZmZiZnZoVHBtRmJpa0NLcmFsUWg4TjE4WWZYbWV3dDFqQnlRL1Q4OWhldVlrNXJTYWduM2Y5M3YycDczWHlYUGxQNzFTV3Y1ZFpjWG5wUndmNWcwbS8xd3g4T3R1dFEiLCJtYWMiOiIzMzFmNTIxMjdjMzIzMzUzYjQ5YjEzNGNkZDQ4Yjc4MjU1ZDEzMmU0MjA2ZjQ1MGI5YTlkZDhmN2E3MWJlYjBmIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IkpzYU54dTg3dENzb3lGaXA0RUZvQ0E9PSIsInZhbHVlIjoicFhYNDZXODRFRUhnRGZXUkhMWlhMa3crQi9IS084WXJaNEFyelhDTGJCb25hRnVpNUI2M082RWp6QXV6dElLWURjUktXbW03RlhucURhc050R0tNTjBJZjBBTk4yT1VUOTR3VWg3MVpxSm5LVUFMZW9neWExQVVkbGF4MGlQQVoiLCJtYWMiOiIzNWFiNGQ0N2FiZTQ4MDNlNGM5MzhmYzExOTNlNDBmNzBlMDVlNGI2YmRjOGE4ODJiZWZmZDVmZTM0NDU3NTRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://mlk.test/iphone-ip15-pro-max</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264877447\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1501672113 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjJzTWowQmlYZ0pKeDFTbkoxSTZGVlE9PSIsInZhbHVlIjoiVHFSVC9laWtOZHdWTzhHRUNiTk1NQWs2RzkwbEUxcEM1ZmZiZnZoVHBtRmJpa0NLcmFsUWg4TjE4WWZYbWV3dDFqQnlRL1Q4OWhldVlrNXJTYWduM2Y5M3YycDczWHlYUGxQNzFTV3Y1ZFpjWG5wUndmNWcwbS8xd3g4T3R1dFEiLCJtYWMiOiIzMzFmNTIxMjdjMzIzMzUzYjQ5YjEzNGNkZDQ4Yjc4MjU1ZDEzMmU0MjA2ZjQ1MGI5YTlkZDhmN2E3MWJlYjBmIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkpzYU54dTg3dENzb3lGaXA0RUZvQ0E9PSIsInZhbHVlIjoicFhYNDZXODRFRUhnRGZXUkhMWlhMa3crQi9IS084WXJaNEFyelhDTGJCb25hRnVpNUI2M082RWp6QXV6dElLWURjUktXbW03RlhucURhc050R0tNTjBJZjBBTk4yT1VUOTR3VWg3MVpxSm5LVUFMZW9neWExQVVkbGF4MGlQQVoiLCJtYWMiOiIzNWFiNGQ0N2FiZTQ4MDNlNGM5MzhmYzExOTNlNDBmNzBlMDVlNGI2YmRjOGE4ODJiZWZmZDVmZTM0NDU3NTRjIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1501672113\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-858253262 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">63768</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">c387c041f9de5862457c03b84a088070</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:41:41 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-858253262\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1132730367 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1132730367\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/243/AhBbSpkRhfGYo92QTqXBT9guHQqTl4aap68KZLYo.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}