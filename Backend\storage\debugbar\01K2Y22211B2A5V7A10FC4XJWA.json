{"__meta": {"id": "01K2Y22211B2A5V7A10FC4XJWA", "datetime": "2025-08-18 08:42:18", "utime": **********.145821, "method": "GET", "uri": "/cache/original/product/245/GfBUM7npOtUOGc3cfRruV74OhjQFBjkwP58mG8sF.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755502937.976087, "end": **********.156016, "duration": 0.17992901802062988, "duration_str": "180ms", "measures": [{"label": "Booting", "start": 1755502937.976087, "relative_start": 0, "end": **********.12995, "relative_end": **********.12995, "duration": 0.*****************, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.12996, "relative_start": 0.*****************, "end": **********.156018, "relative_end": 1.9073486328125e-06, "duration": 0.026057958602905273, "duration_str": "26.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.139184, "relative_start": 0.*****************, "end": **********.142387, "relative_end": **********.142387, "duration": 0.0032029151916503906, "duration_str": "3.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.14457, "relative_start": 0.*****************, "end": **********.144661, "relative_end": **********.144661, "duration": 9.083747863769531e-05, "duration_str": "91μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.144674, "relative_start": 0.*****************, "end": **********.144685, "relative_end": **********.144685, "duration": 1.0967254638671875e-05, "duration_str": "11μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/245/GfBUM7npOtUOGc3cfRruV74OhjQFBjkwP58mG8sF.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "180ms", "peak_memory": "40MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-709869472 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-709869472\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-721767620 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-721767620\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1021711115 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IkV2NFlKTWI2enc4Skt6Qjg5dVNyYnc9PSIsInZhbHVlIjoiaFF3S1lYdzI5SnhUak51Ynh6cnRZTFFyUzhYeS9qcVNKaDlTbmZydXBKWjhXelNFdDQ3OXcrRSszeTduOC8wb29SZWloUkNXOGpyZFNOcmVBM3IybzhjazRVK0tMcS9GWm9xbWwzYjVDRGppZzhyMnJtbHhvYTZhc01RblZha2QiLCJtYWMiOiJiNDg1NjQyZDY2ZmYxZTEzN2EzODBiMzg4M2M2MTlhYjQyMjM2MmU4NmQyYTkyYzhmMTAyYTdjNzZlZjdiNjI1IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IkZ1Q1ViYnFwTGpPUUtMS254bXdkd2c9PSIsInZhbHVlIjoiSU53NEY1enpLdHJWL0NLZXU2Uml4Qm0zbUNocXJiWUNXOWFBWWdyQWtvRjg4QkgzeU15SDF1ZnkxNGhCMjNxWVRQNGZVcEJSbHQreTZyWkV6c250QTA4VkJJT29XdnRkTktyR2hraXVaYTNBUE5Yc2owVlRQaWVkemwzZytjeGciLCJtYWMiOiI0MzdjNzdhMjE0NWJkMjMxZDc4NmNjYjY2NWEyZjFlMDNiYjg3NjBlYzQxMzlhMDQ1Yzk1NDVlY2IyM2E0MTZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-C<PERSON>,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://mlk.test/iphone-ip1314</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1021711115\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1138206290 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkV2NFlKTWI2enc4Skt6Qjg5dVNyYnc9PSIsInZhbHVlIjoiaFF3S1lYdzI5SnhUak51Ynh6cnRZTFFyUzhYeS9qcVNKaDlTbmZydXBKWjhXelNFdDQ3OXcrRSszeTduOC8wb29SZWloUkNXOGpyZFNOcmVBM3IybzhjazRVK0tMcS9GWm9xbWwzYjVDRGppZzhyMnJtbHhvYTZhc01RblZha2QiLCJtYWMiOiJiNDg1NjQyZDY2ZmYxZTEzN2EzODBiMzg4M2M2MTlhYjQyMjM2MmU4NmQyYTkyYzhmMTAyYTdjNzZlZjdiNjI1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZ1Q1ViYnFwTGpPUUtMS254bXdkd2c9PSIsInZhbHVlIjoiSU53NEY1enpLdHJWL0NLZXU2Uml4Qm0zbUNocXJiWUNXOWFBWWdyQWtvRjg4QkgzeU15SDF1ZnkxNGhCMjNxWVRQNGZVcEJSbHQreTZyWkV6c250QTA4VkJJT29XdnRkTktyR2hraXVaYTNBUE5Yc2owVlRQaWVkemwzZytjeGciLCJtYWMiOiI0MzdjNzdhMjE0NWJkMjMxZDc4NmNjYjY2NWEyZjFlMDNiYjg3NjBlYzQxMzlhMDQ1Yzk1NDVlY2IyM2E0MTZmIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138206290\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-653004247 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">13566</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ab2efeb2e316d9e303410cc0fc291f84</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:42:18 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653004247\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1939094798 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1939094798\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/245/GfBUM7npOtUOGc3cfRruV74OhjQFBjkwP58mG8sF.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}