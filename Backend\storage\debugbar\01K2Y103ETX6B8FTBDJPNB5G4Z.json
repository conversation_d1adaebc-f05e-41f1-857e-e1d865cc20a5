{"__meta": {"id": "01K2Y103ETX6B8FTBDJPNB5G4Z", "datetime": "2025-08-18 08:23:45", "utime": **********.49882, "method": "GET", "uri": "/cache/large/product/226/15xoSkk7xNllpRbg2tP8cnSqLkVHhSsLfzjSPn9K.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.323619, "end": **********.50784, "duration": 0.1842210292816162, "duration_str": "184ms", "measures": [{"label": "Booting", "start": **********.323619, "relative_start": 0, "end": **********.481083, "relative_end": **********.481083, "duration": 0.*****************, "duration_str": "157ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.481094, "relative_start": 0.*****************, "end": **********.507842, "relative_end": 2.1457672119140625e-06, "duration": 0.026748180389404297, "duration_str": "26.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.490334, "relative_start": 0.*****************, "end": **********.49355, "relative_end": **********.49355, "duration": 0.0032160282135009766, "duration_str": "3.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.497609, "relative_start": 0.*****************, "end": **********.497694, "relative_end": **********.497694, "duration": 8.511543273925781e-05, "duration_str": "85μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.497705, "relative_start": 0.****************, "end": **********.497716, "relative_end": **********.497716, "duration": 1.0967254638671875e-05, "duration_str": "11μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/product/226/15xoSkk7xNllpRbg2tP8cnSqLkVHhSsLfzjSPn9K.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "185ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1738450928 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1738450928\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1236077496 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1236077496\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2126776934 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6ImdVZ29YSW1Sci91OTBtV3lhdEcrVUE9PSIsInZhbHVlIjoiOGhzZ2tYakpjemk2TG5VSDhhUThYNHF2M0pac1VPZjNYSGNub3RYMk5VOVJjTm5OdU9oaVBaeUVCWUVOZnZaREZoQmkzUHEyWDZWY0NBdUNGS2pqT3IwTEhENmRMMGVVWStQMTB0bkxycm9XMGIxMHNUaWxybFZTUGJTK2VqbmUiLCJtYWMiOiJkYjEyMTgwYTFiMTdmNzlhNzBhMTFmMGY4YjgzZjQ5ODZiYzdhOTk2ZGZhZDcxMzlkZjk2MjkwN2IwMGYzNDExIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6ImsvT3pzckpxWkd3cUQwOGFnaEJTclE9PSIsInZhbHVlIjoiUkVJakR5ZTkxbWxLU2N1OXRxbng4U0x2NzdhbFdkUWtxMWhSaG9Md0FRR3NKZk1JUUlHcW84dDl4NWNBQTBDZVI5MUs3K2NYZUlEZWtLWm1ycmR2R1RyKzYxNENJOVBxNDVKS2w1alZmMzdFTmRPOXVJenBOdG9aQjlVcHBTWmEiLCJtYWMiOiJlYWViYzAwMDJhOTdiMGViNzQ2YzcwZjY1NDRmNmI4ZGY0ZjE3OWQxMTg3ZjI4ODY2MDk3ODkyZmZiMTEyMDlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://mlk.test/1022-iphone-ip15-pro-max</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126776934\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-448814805 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImdVZ29YSW1Sci91OTBtV3lhdEcrVUE9PSIsInZhbHVlIjoiOGhzZ2tYakpjemk2TG5VSDhhUThYNHF2M0pac1VPZjNYSGNub3RYMk5VOVJjTm5OdU9oaVBaeUVCWUVOZnZaREZoQmkzUHEyWDZWY0NBdUNGS2pqT3IwTEhENmRMMGVVWStQMTB0bkxycm9XMGIxMHNUaWxybFZTUGJTK2VqbmUiLCJtYWMiOiJkYjEyMTgwYTFiMTdmNzlhNzBhMTFmMGY4YjgzZjQ5ODZiYzdhOTk2ZGZhZDcxMzlkZjk2MjkwN2IwMGYzNDExIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImsvT3pzckpxWkd3cUQwOGFnaEJTclE9PSIsInZhbHVlIjoiUkVJakR5ZTkxbWxLU2N1OXRxbng4U0x2NzdhbFdkUWtxMWhSaG9Md0FRR3NKZk1JUUlHcW84dDl4NWNBQTBDZVI5MUs3K2NYZUlEZWtLWm1ycmR2R1RyKzYxNENJOVBxNDVKS2w1alZmMzdFTmRPOXVJenBOdG9aQjlVcHBTWmEiLCJtYWMiOiJlYWViYzAwMDJhOTdiMGViNzQ2YzcwZjY1NDRmNmI4ZGY0ZjE3OWQxMTg3ZjI4ODY2MDk3ODkyZmZiMTEyMDlhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-448814805\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-123754920 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">12710</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">357e248373dcd00e07e4a9d77c95c9db</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:23:45 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123754920\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-983336816 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-983336816\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/product/226/15xoSkk7xNllpRbg2tP8cnSqLkVHhSsLfzjSPn9K.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}