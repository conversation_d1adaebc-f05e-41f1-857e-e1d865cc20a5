{"__meta": {"id": "01K2Y11NX1FD7Z4BKTFSRYQ08E", "datetime": "2025-08-18 08:24:37", "utime": **********.153938, "method": "GET", "uri": "/api/checkout/cart", "ip": "127.0.0.1"}, "modules": {"count": 10, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\AttributeFamily (9)", "Webkul\\Attribute\\Models\\Attribute (32)"], "views": [], "queries": [{"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "duration": 4.73, "duration_str": "4.73s", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 1.83, "duration_str": "1.83s", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}]}, {"name": "Webkul\\CartRule", "models": ["Webkul\\CartRule\\Models\\CartRule (2)", "Webkul\\CartRule\\Models\\CartRuleCoupon (2)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `cart_rules` left join `cart_rule_customer_groups` on `cart_rules`.`id` = `cart_rule_customer_groups`.`cart_rule_id` left join `cart_rule_channels` on `cart_rules`.`id` = `cart_rule_channels`.`cart_rule_id` where `cart_rule_customer_groups`.`customer_group_id` = 1 and `cart_rule_channels`.`channel_id` = 1 and (`cart_rules`.`starts_from` <= '2025-08-18 08:08:37' or `cart_rules`.`starts_from` is null) and (`cart_rules`.`ends_till` >= '2025-08-18 08:08:37' or `cart_rules`.`ends_till` is null) and `status` = 1", "duration": 5.43, "duration_str": "5.43s", "connection": "mlk"}, {"sql": "select * from `cart_rules` left join `cart_rule_customer_groups` on `cart_rules`.`id` = `cart_rule_customer_groups`.`cart_rule_id` left join `cart_rule_channels` on `cart_rules`.`id` = `cart_rule_channels`.`cart_rule_id` where `cart_rule_customer_groups`.`customer_group_id` = 1 and `cart_rule_channels`.`channel_id` = 1 and (`cart_rules`.`starts_from` <= '2025-08-18 08:08:37' or `cart_rules`.`starts_from` is null) and (`cart_rules`.`ends_till` >= '2025-08-18 08:08:37' or `cart_rules`.`ends_till` is null) and `status` = 1 order by `sort_order` asc", "duration": 0.43, "duration_str": "430ms", "connection": "mlk"}, {"sql": "select * from `cart_rule_coupons` where `cart_rule_coupons`.`cart_rule_id` in (1, 2)", "duration": 1.69, "duration_str": "1.69s", "connection": "mlk"}]}, {"name": "Webkul\\CatalogRule", "models": [], "views": [], "queries": [{"sql": "select * from `catalog_rule_product_prices` where `catalog_rule_product_prices`.`product_id` = 213 and `catalog_rule_product_prices`.`product_id` is not null", "duration": 2.06, "duration_str": "2.06s", "connection": "mlk"}]}, {"name": "Webkul\\Checkout", "models": ["Webkul\\Checkout\\Models\\Cart (2)", "Webkul\\Checkout\\Models\\CartItem (12)"], "views": [], "queries": [{"sql": "select * from `cart` where `cart`.`id` = 37 limit 1", "duration": 2.19, "duration_str": "2.19s", "connection": "mlk"}, {"sql": "select * from `cart_items` where `cart_items`.`cart_id` = 37 and `cart_items`.`cart_id` is not null and `parent_id` is null", "duration": 5.1, "duration_str": "5.1s", "connection": "mlk"}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 92 limit 1", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 94 limit 1", "duration": 0.35, "duration_str": "350ms", "connection": "mlk"}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 96 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "update `cart_items` set `discount_percent` = 0, `discount_amount` = 0, `base_discount_amount` = 0, `cart_items`.`updated_at` = '2025-08-18 08:24:37' where `id` = 92", "duration": 2.34, "duration_str": "2.34s", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `cart_items` where `cart_items`.`parent_id` = 92 and `cart_items`.`parent_id` is not null", "duration": 0.39, "duration_str": "390ms", "connection": "mlk"}, {"sql": "update `cart_items` set `discount_percent` = 0, `discount_amount` = 0, `base_discount_amount` = 0, `cart_items`.`updated_at` = '2025-08-18 08:24:37' where `id` = 94", "duration": 2.14, "duration_str": "2.14s", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `cart_items` where `cart_items`.`parent_id` = 94 and `cart_items`.`parent_id` is not null", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "update `cart_items` set `discount_percent` = 0, `discount_amount` = 0, `base_discount_amount` = 0, `cart_items`.`updated_at` = '2025-08-18 08:24:37' where `id` = 96", "duration": 2.03, "duration_str": "2.03s", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `cart_items` where `cart_items`.`parent_id` = 96 and `cart_items`.`parent_id` is not null", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `cart_shipping_rates` where `cart_shipping_rates`.`cart_id` = 37 and `cart_shipping_rates`.`cart_id` is not null", "duration": 1.86, "duration_str": "1.86s", "connection": "mlk"}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 37 and `addresses`.`cart_id` is not null and `address_type` = 'cart_shipping' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 2.12, "duration_str": "2.12s", "connection": "mlk"}, {"sql": "update `cart_items` set `tax_percent` = 0, `tax_amount` = 0, `base_tax_amount` = 0, `cart_items`.`updated_at` = '2025-08-18 08:24:37' where `id` = 92", "duration": 2.13, "duration_str": "2.13s", "connection": "mlk"}, {"sql": "update `cart_items` set `tax_percent` = 0, `tax_amount` = 0, `base_tax_amount` = 0, `cart_items`.`updated_at` = '2025-08-18 08:24:37' where `id` = 94", "duration": 2.05, "duration_str": "2.05s", "connection": "mlk"}, {"sql": "update `cart_items` set `tax_percent` = 0, `tax_amount` = 0, `base_tax_amount` = 0, `cart_items`.`updated_at` = '2025-08-18 08:24:37' where `id` = 96", "duration": 2.17, "duration_str": "2.17s", "connection": "mlk"}, {"sql": "select * from `cart` where `cart`.`id` = 37 limit 1", "duration": 1.76, "duration_str": "1.76s", "connection": "mlk"}, {"sql": "select * from `cart_items` where `cart_items`.`cart_id` = 37 and `cart_items`.`cart_id` is not null and `parent_id` is null", "duration": 3.23, "duration_str": "3.23s", "connection": "mlk"}, {"sql": "select * from `cart_shipping_rates` where `cart_shipping_rates`.`cart_id` = 37 and `cart_shipping_rates`.`cart_id` is not null", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "update `cart` set `items_qty` = 4, `grand_total` = 4, `base_grand_total` = 4, `sub_total` = 4, `base_sub_total` = 4, `tax_total` = 0, `base_tax_total` = 0, `discount_amount` = 0, `base_discount_amount` = 0, `shipping_amount` = 0, `base_shipping_amount` = 0, `shipping_amount_incl_tax` = 0, `base_shipping_amount_incl_tax` = 0, `sub_total_incl_tax` = 4, `base_sub_total_incl_tax` = 4, `cart`.`updated_at` = '2025-08-18 08:24:37' where `id` = 37", "duration": 2.06, "duration_str": "2.06s", "connection": "mlk"}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 37 and `addresses`.`cart_id` is not null and `address_type` = 'cart_billing' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 37 and `addresses`.`cart_id` is not null and `address_type` = 'cart_shipping' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `cart_payment` where `cart_payment`.`cart_id` = 37 and `cart_payment`.`cart_id` is not null limit 1", "duration": 1.79, "duration_str": "1.79s", "connection": "mlk"}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 92 limit 1", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 94 limit 1", "duration": 0.32, "duration_str": "320ms", "connection": "mlk"}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 96 limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (3)", "Webkul\\Core\\Models\\Locale (5)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 20.75, "duration_str": "20.75s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 2", "duration": 1.79, "duration_str": "1.79s", "connection": "mlk"}, {"sql": "select `channels`.*, `cart_rule_channels`.`cart_rule_id` as `pivot_cart_rule_id`, `cart_rule_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `cart_rule_channels` on `channels`.`id` = `cart_rule_channels`.`channel_id` where `cart_rule_channels`.`cart_rule_id` in (1, 2)", "duration": 1.77, "duration_str": "1.77s", "connection": "mlk"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (7)"], "views": [], "queries": [{"sql": "select * from `customer_groups` where `code` = 'guest'", "duration": 1.68, "duration_str": "1.68s", "connection": "mlk"}, {"sql": "select `customer_groups`.*, `cart_rule_customer_groups`.`cart_rule_id` as `pivot_cart_rule_id`, `cart_rule_customer_groups`.`customer_group_id` as `pivot_customer_group_id` from `customer_groups` inner join `cart_rule_customer_groups` on `customer_groups`.`id` = `cart_rule_customer_groups`.`customer_group_id` where `cart_rule_customer_groups`.`cart_rule_id` in (1, 2)", "duration": 0.31, "duration_str": "310ms", "connection": "mlk"}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 37 and `addresses`.`cart_id` is not null and `address_type` = 'cart_shipping' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 2.12, "duration_str": "2.12s", "connection": "mlk"}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 37 and `addresses`.`cart_id` is not null and `address_type` = 'cart_billing' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 37 and `addresses`.`cart_id` is not null and `address_type` = 'cart_shipping' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (12)", "Webkul\\Product\\Models\\ProductAttributeValue (237)", "Webkul\\Product\\Models\\ProductPriceIndex (6)", "Webkul\\Product\\Models\\ProductImage (5)"], "views": [], "queries": [{"sql": "select * from `products` where `products`.`id` = 211 and `products`.`id` is not null limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "duration": 2.01, "duration_str": "2.01s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 213 and `products`.`id` is not null limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 213 and `product_attribute_values`.`product_id` is not null", "duration": 0.32, "duration_str": "320ms", "connection": "mlk"}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 213 and `product_customer_group_prices`.`product_id` is not null", "duration": 1.68, "duration_str": "1.68s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 211 and `products`.`id` is not null limit 1", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "duration": 0.34, "duration_str": "340ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 215 and `products`.`id` is not null limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 215 and `product_attribute_values`.`product_id` is not null", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` = 215 and `product_price_indices`.`product_id` is not null", "duration": 1.94, "duration_str": "1.94s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 211 and `products`.`id` is not null limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "duration": 0.31, "duration_str": "310ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 212 and `products`.`id` is not null limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 212 and `product_attribute_values`.`product_id` is not null", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` = 212 and `product_price_indices`.`product_id` is not null", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 211 and `products`.`id` is not null limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 213 and `products`.`id` is not null limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 213 and `product_images`.`product_id` is not null order by `position` asc", "duration": 1.9, "duration_str": "1.9s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "duration": 1.87, "duration_str": "1.87s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 211 and `products`.`id` is not null limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 215 and `products`.`id` is not null limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 215 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 211 and `products`.`id` is not null limit 1", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 212 and `products`.`id` is not null limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 212 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "duration": 0.34, "duration_str": "340ms", "connection": "mlk"}]}, {"name": "Webkul\\Sales", "models": [], "views": [], "queries": [{"sql": "select * from `addresses` where `addresses`.`cart_id` = 37 and `addresses`.`cart_id` is not null and `address_type` = 'cart_shipping' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 2.12, "duration_str": "2.12s", "connection": "mlk"}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 37 and `addresses`.`cart_id` is not null and `address_type` = 'cart_billing' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 37 and `addresses`.`cart_id` is not null and `address_type` = 'cart_shipping' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}]}, {"name": "Webkul\\Tax", "models": ["Webkul\\Tax\\Models\\TaxCategory (1)"], "views": [], "queries": [{"sql": "select * from `tax_categories` where `tax_categories`.`id` = 1 limit 1", "duration": 1.74, "duration_str": "1.74s", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": [], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}]}]}, "messages": {"count": 9, "messages": [{"message": "[08:24:37] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php on line 128", "message_html": null, "is_string": false, "label": "warning", "time": **********.064866, "xdebug_link": null, "collector": "log"}, {"message": "[08:24:37] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php on line 128", "message_html": null, "is_string": false, "label": "warning", "time": **********.064955, "xdebug_link": null, "collector": "log"}, {"message": "[08:24:37] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php on line 128", "message_html": null, "is_string": false, "label": "warning", "time": **********.070047, "xdebug_link": null, "collector": "log"}, {"message": "[08:24:37] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php on line 128", "message_html": null, "is_string": false, "label": "warning", "time": **********.070122, "xdebug_link": null, "collector": "log"}, {"message": "[08:24:37] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php on line 128", "message_html": null, "is_string": false, "label": "warning", "time": **********.073721, "xdebug_link": null, "collector": "log"}, {"message": "[08:24:37] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php on line 128", "message_html": null, "is_string": false, "label": "warning", "time": **********.073796, "xdebug_link": null, "collector": "log"}, {"message": "[08:24:37] LOG.warning: strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Tax\\src\\Tax.php on line 120", "message_html": null, "is_string": false, "label": "warning", "time": **********.088704, "xdebug_link": null, "collector": "log"}, {"message": "[08:24:37] LOG.warning: strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Tax\\src\\Tax.php on line 120", "message_html": null, "is_string": false, "label": "warning", "time": **********.094875, "xdebug_link": null, "collector": "log"}, {"message": "[08:24:37] LOG.warning: strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Tax\\src\\Tax.php on line 120", "message_html": null, "is_string": false, "label": "warning", "time": **********.100895, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.73049, "end": **********.161235, "duration": 0.43074512481689453, "duration_str": "431ms", "measures": [{"label": "Booting", "start": **********.73049, "relative_start": 0, "end": **********.908964, "relative_end": **********.908964, "duration": 0.*****************, "duration_str": "178ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.908976, "relative_start": 0.*****************, "end": **********.16124, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "252ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.920403, "relative_start": 0.*****************, "end": **********.924052, "relative_end": **********.924052, "duration": 0.003648996353149414, "duration_str": "3.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.116503, "relative_start": 0.****************, "end": **********.152778, "relative_end": **********.152778, "duration": 0.*****************, "duration_str": "36.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "it"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 80, "nb_statements": 80, "nb_visible_statements": 80, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09820000000000004, "accumulated_duration_str": "98.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "theme", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.940845, "duration": 0.02075, "duration_str": "20.75ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 21.13}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.9661858, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 21.13, "width_percent": 0.255}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.9683418, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 21.385, "width_percent": 0.244}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.973465, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 21.629, "width_percent": 0.397}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.974329, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 22.026, "width_percent": 0.214}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.975015, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 22.24, "width_percent": 0.214}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.975732, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 22.454, "width_percent": 0.163}, {"sql": "select * from `cart` where `cart`.`id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 79}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}], "start": **********.980852, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 22.617, "width_percent": 2.23}, {"sql": "select * from `cart_items` where `cart_items`.`cart_id` = 37 and `cart_items`.`cart_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 918}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 815}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.984705, "duration": 0.0050999999999999995, "duration_str": "5.1ms", "memory": 0, "memory_str": null, "filename": "Cart.php:918", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 918}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FCart.php&line=918", "ajax": false, "filename": "Cart.php", "line": "918"}, "connection": "mlk", "explain": null, "start_percent": 24.847, "width_percent": 5.193}, {"sql": "select * from `products` where `products`.`id` = 211 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/CartItem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\CartItem.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 927}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 815}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9946449, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "CartItem.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/CartItem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\CartItem.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCartItem.php&line=45", "ajax": false, "filename": "CartItem.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 30.041, "width_percent": 0.224}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 940}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}], "start": **********.995982, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 30.265, "width_percent": 0.193}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 190}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 940}], "start": **********.996784, "duration": 0.004730000000000001, "duration_str": "4.73ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mlk", "explain": null, "start_percent": 30.458, "width_percent": 4.817}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 940}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}], "start": **********.002889, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 35.275, "width_percent": 2.047}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 92 limit 1", "type": "query", "params": [], "bindings": [92], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 956}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 927}, {"index": 24, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 815}, {"index": 26, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}], "start": **********.0064762, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:956", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 956}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=956", "ajax": false, "filename": "AbstractType.php", "line": "956"}, "connection": "mlk", "explain": null, "start_percent": 37.322, "width_percent": 0.285}, {"sql": "select * from `products` where `products`.`id` = 213 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 957}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 927}, {"index": 24, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 815}, {"index": 26, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}], "start": **********.00751, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:957", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 957}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=957", "ajax": false, "filename": "AbstractType.php", "line": "957"}, "connection": "mlk", "explain": null, "start_percent": 37.607, "width_percent": 0.183}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 957}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}], "start": **********.008222, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 37.79, "width_percent": 0.173}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 213 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 957}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}], "start": **********.009509, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 37.963, "width_percent": 0.326}, {"sql": "select * from `customer_groups` where `code` = 'guest'", "type": "query", "params": [], "bindings": ["guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 746}, {"index": 19, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}], "start": **********.012331, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 38.289, "width_percent": 1.711}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 213 and `product_customer_group_prices`.`product_id` is not null", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductCustomerGroupPriceRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductCustomerGroupPriceRepository.php", "line": 62}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 165}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 109}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 671}], "start": **********.015222, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 40, "width_percent": 1.711}, {"sql": "select * from `catalog_rule_product_prices` where `catalog_rule_product_prices`.`product_id` = 213 and `catalog_rule_product_prices`.`product_id` is not null", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 214}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 111}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 671}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 558}], "start": **********.018296, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 41.711, "width_percent": 2.098}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 457}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 479}], "start": **********.0243568, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 43.809, "width_percent": 1.823}, {"sql": "select * from `products` where `products`.`id` = 211 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/CartItem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\CartItem.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 927}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 815}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.027163, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "CartItem.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/CartItem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\CartItem.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCartItem.php&line=45", "ajax": false, "filename": "CartItem.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 45.631, "width_percent": 0.275}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 940}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}], "start": **********.02792, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 45.906, "width_percent": 0.173}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 940}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}], "start": **********.02902, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 46.079, "width_percent": 0.346}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 94 limit 1", "type": "query", "params": [], "bindings": [94], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 956}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 927}, {"index": 24, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 815}, {"index": 26, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}], "start": **********.030289, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:956", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 956}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=956", "ajax": false, "filename": "AbstractType.php", "line": "956"}, "connection": "mlk", "explain": null, "start_percent": 46.426, "width_percent": 0.356}, {"sql": "select * from `products` where `products`.`id` = 215 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [215], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 957}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 927}, {"index": 24, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 815}, {"index": 26, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}], "start": **********.031098, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:957", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 957}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=957", "ajax": false, "filename": "AbstractType.php", "line": "957"}, "connection": "mlk", "explain": null, "start_percent": 46.782, "width_percent": 0.193}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 957}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}], "start": **********.031782, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 46.976, "width_percent": 0.173}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 215 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [215], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 957}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}], "start": **********.03301, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 47.149, "width_percent": 0.295}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` = 215 and `product_price_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [215], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 684}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 600}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 661}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 558}], "start": **********.034645, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 47.444, "width_percent": 1.976}, {"sql": "select * from `products` where `products`.`id` = 211 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/CartItem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\CartItem.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 927}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 815}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.03916, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "CartItem.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/CartItem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\CartItem.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCartItem.php&line=45", "ajax": false, "filename": "CartItem.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 49.42, "width_percent": 0.214}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 940}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}], "start": **********.039986, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 49.633, "width_percent": 0.214}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 940}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}], "start": **********.0413392, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 49.847, "width_percent": 0.316}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 96 limit 1", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 956}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 927}, {"index": 24, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 815}, {"index": 26, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}], "start": **********.042533, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:956", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 956}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=956", "ajax": false, "filename": "AbstractType.php", "line": "956"}, "connection": "mlk", "explain": null, "start_percent": 50.163, "width_percent": 0.224}, {"sql": "select * from `products` where `products`.`id` = 212 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 957}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 927}, {"index": 24, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 815}, {"index": 26, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}], "start": **********.043211, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:957", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 957}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=957", "ajax": false, "filename": "AbstractType.php", "line": "957"}, "connection": "mlk", "explain": null, "start_percent": 50.387, "width_percent": 0.173}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 957}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}], "start": **********.0438151, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 50.56, "width_percent": 0.143}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 212 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 957}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 552}], "start": **********.0448549, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 50.703, "width_percent": 0.265}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` = 212 and `product_price_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 684}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 600}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 661}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 558}], "start": **********.046343, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 50.967, "width_percent": 0.193}, {"sql": "select count(*) as aggregate from `cart_rules` left join `cart_rule_customer_groups` on `cart_rules`.`id` = `cart_rule_customer_groups`.`cart_rule_id` left join `cart_rule_channels` on `cart_rules`.`id` = `cart_rule_channels`.`cart_rule_id` where `cart_rule_customer_groups`.`customer_group_id` = 1 and `cart_rule_channels`.`channel_id` = 1 and (`cart_rules`.`starts_from` <= '2025-08-18 08:08:37' or `cart_rules`.`starts_from` is null) and (`cart_rules`.`ends_till` >= '2025-08-18 08:08:37' or `cart_rules`.`ends_till` is null) and `status` = 1", "type": "query", "params": [], "bindings": [1, 1, "2025-08-18 08:08:37", "2025-08-18 08:08:37", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 566}, {"index": 17, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 62}, {"index": 18, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 826}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}], "start": **********.050917, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "CartRule.php:566", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FHelpers%2FCartRule.php&line=566", "ajax": false, "filename": "CartRule.php", "line": "566"}, "connection": "mlk", "explain": null, "start_percent": 51.161, "width_percent": 5.53}, {"sql": "select * from `cart_rules` left join `cart_rule_customer_groups` on `cart_rules`.`id` = `cart_rule_customer_groups`.`cart_rule_id` left join `cart_rule_channels` on `cart_rules`.`id` = `cart_rule_channels`.`cart_rule_id` where `cart_rule_customer_groups`.`customer_group_id` = 1 and `cart_rule_channels`.`channel_id` = 1 and (`cart_rules`.`starts_from` <= '2025-08-18 08:08:37' or `cart_rules`.`starts_from` is null) and (`cart_rules`.`ends_till` >= '2025-08-18 08:08:37' or `cart_rules`.`ends_till` is null) and `status` = 1 order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 1, "2025-08-18 08:08:37", "2025-08-18 08:08:37", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 115}, {"index": 16, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 461}, {"index": 17, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 70}, {"index": 18, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 826}], "start": **********.0570629, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CartRule.php:115", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 115}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FHelpers%2FCartRule.php&line=115", "ajax": false, "filename": "CartRule.php", "line": "115"}, "connection": "mlk", "explain": null, "start_percent": 56.69, "width_percent": 0.438}, {"sql": "select `customer_groups`.*, `cart_rule_customer_groups`.`cart_rule_id` as `pivot_cart_rule_id`, `cart_rule_customer_groups`.`customer_group_id` as `pivot_customer_group_id` from `customer_groups` inner join `cart_rule_customer_groups` on `customer_groups`.`id` = `cart_rule_customer_groups`.`customer_group_id` where `cart_rule_customer_groups`.`cart_rule_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 115}, {"index": 20, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 461}, {"index": 21, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 70}, {"index": 22, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}, {"index": 27, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 826}], "start": **********.058213, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CartRule.php:115", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 115}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FHelpers%2FCartRule.php&line=115", "ajax": false, "filename": "CartRule.php", "line": "115"}, "connection": "mlk", "explain": null, "start_percent": 57.128, "width_percent": 0.316}, {"sql": "select `channels`.*, `cart_rule_channels`.`cart_rule_id` as `pivot_cart_rule_id`, `cart_rule_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `cart_rule_channels` on `channels`.`id` = `cart_rule_channels`.`channel_id` where `cart_rule_channels`.`cart_rule_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 115}, {"index": 20, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 461}, {"index": 21, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 70}, {"index": 22, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}, {"index": 27, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 826}], "start": **********.059203, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "CartRule.php:115", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 115}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FHelpers%2FCartRule.php&line=115", "ajax": false, "filename": "CartRule.php", "line": "115"}, "connection": "mlk", "explain": null, "start_percent": 57.444, "width_percent": 1.802}, {"sql": "select * from `cart_rule_coupons` where `cart_rule_coupons`.`cart_rule_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 115}, {"index": 21, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 461}, {"index": 22, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 70}, {"index": 23, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}, {"index": 28, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 826}], "start": **********.061707, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "CartRule.php:115", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 115}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FHelpers%2FCartRule.php&line=115", "ajax": false, "filename": "CartRule.php", "line": "115"}, "connection": "mlk", "explain": null, "start_percent": 59.246, "width_percent": 1.721}, {"sql": "update `cart_items` set `discount_percent` = 0, `discount_amount` = 0, `base_discount_amount` = 0, `cart_items`.`updated_at` = '2025-08-18 08:24:37' where `id` = 92", "type": "query", "params": [], "bindings": [0, 0, 0, "2025-08-18 08:24:37", 92], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 302}, {"index": 15, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 73}, {"index": 16, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 826}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}], "start": **********.065295, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "CartRule.php:302", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FHelpers%2FCartRule.php&line=302", "ajax": false, "filename": "CartRule.php", "line": "302"}, "connection": "mlk", "explain": null, "start_percent": 60.967, "width_percent": 2.383}, {"sql": "select count(*) as aggregate from `cart_items` where `cart_items`.`parent_id` = 92 and `cart_items`.`parent_id` is not null", "type": "query", "params": [], "bindings": [92], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 78}, {"index": 20, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}, {"index": 25, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 826}, {"index": 27, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0692248, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CartRule.php:78", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FHelpers%2FCartRule.php&line=78", "ajax": false, "filename": "CartRule.php", "line": "78"}, "connection": "mlk", "explain": null, "start_percent": 63.35, "width_percent": 0.397}, {"sql": "update `cart_items` set `discount_percent` = 0, `discount_amount` = 0, `base_discount_amount` = 0, `cart_items`.`updated_at` = '2025-08-18 08:24:37' where `id` = 94", "type": "query", "params": [], "bindings": [0, 0, 0, "2025-08-18 08:24:37", 94], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 302}, {"index": 15, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 73}, {"index": 16, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 826}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}], "start": **********.0704439, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "CartRule.php:302", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FHelpers%2FCartRule.php&line=302", "ajax": false, "filename": "CartRule.php", "line": "302"}, "connection": "mlk", "explain": null, "start_percent": 63.747, "width_percent": 2.179}, {"sql": "select count(*) as aggregate from `cart_items` where `cart_items`.`parent_id` = 94 and `cart_items`.`parent_id` is not null", "type": "query", "params": [], "bindings": [94], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 78}, {"index": 20, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}, {"index": 25, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 826}, {"index": 27, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.073122, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "CartRule.php:78", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FHelpers%2FCartRule.php&line=78", "ajax": false, "filename": "CartRule.php", "line": "78"}, "connection": "mlk", "explain": null, "start_percent": 65.927, "width_percent": 0.224}, {"sql": "update `cart_items` set `discount_percent` = 0, `discount_amount` = 0, `base_discount_amount` = 0, `cart_items`.`updated_at` = '2025-08-18 08:24:37' where `id` = 96", "type": "query", "params": [], "bindings": [0, 0, 0, "2025-08-18 08:24:37", 96], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 302}, {"index": 15, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 73}, {"index": 16, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 826}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}], "start": **********.074084, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "CartRule.php:302", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FHelpers%2FCartRule.php&line=302", "ajax": false, "filename": "CartRule.php", "line": "302"}, "connection": "mlk", "explain": null, "start_percent": 66.151, "width_percent": 2.067}, {"sql": "select count(*) as aggregate from `cart_items` where `cart_items`.`parent_id` = 96 and `cart_items`.`parent_id` is not null", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 78}, {"index": 20, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}, {"index": 25, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 826}, {"index": 27, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.076715, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "CartRule.php:78", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FHelpers%2FCartRule.php&line=78", "ajax": false, "filename": "CartRule.php", "line": "78"}, "connection": "mlk", "explain": null, "start_percent": 68.218, "width_percent": 0.204}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'mlk' and table_name = 'cart' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 85}, {"index": 18, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 826}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0773392, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "CartRule.php:85", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FHelpers%2FCartRule.php&line=85", "ajax": false, "filename": "CartRule.php", "line": "85"}, "connection": "mlk", "explain": null, "start_percent": 68.422, "width_percent": 0.662}, {"sql": "select * from `cart_shipping_rates` where `cart_shipping_rates`.`cart_id` = 37 and `cart_shipping_rates`.`cart_id` is not null", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 106}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 115}, {"index": 27, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 314}, {"index": 28, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 89}, {"index": 29, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}], "start": **********.078753, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "Cart.php:106", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCart.php&line=106", "ajax": false, "filename": "Cart.php", "line": "106"}, "connection": "mlk", "explain": null, "start_percent": 69.084, "width_percent": 1.894}, {"sql": "select * from `tax_categories` where `tax_categories`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 1001}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 828}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.08203, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 70.978, "width_percent": 1.772}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 37 and `addresses`.`cart_id` is not null and `address_type` = 'cart_shipping' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "type": "query", "params": [], "bindings": [37, "cart_shipping", "cart_billing", "cart_shipping"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 1016}, {"index": 22, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 828}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.085217, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "Cart.php:1016", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FCart.php&line=1016", "ajax": false, "filename": "Cart.php", "line": "1016"}, "connection": "mlk", "explain": null, "start_percent": 72.749, "width_percent": 2.159}, {"sql": "update `cart_items` set `tax_percent` = 0, `tax_amount` = 0, `base_tax_amount` = 0, `cart_items`.`updated_at` = '2025-08-18 08:24:37' where `id` = 92", "type": "query", "params": [], "bindings": [0, 0, 0, "2025-08-18 08:24:37", 92], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 1079}, {"index": 15, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 828}, {"index": 17, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.0901349, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "Cart.php:1079", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 1079}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FCart.php&line=1079", "ajax": false, "filename": "Cart.php", "line": "1079"}, "connection": "mlk", "explain": null, "start_percent": 74.908, "width_percent": 2.169}, {"sql": "update `cart_items` set `tax_percent` = 0, `tax_amount` = 0, `base_tax_amount` = 0, `cart_items`.`updated_at` = '2025-08-18 08:24:37' where `id` = 94", "type": "query", "params": [], "bindings": [0, 0, 0, "2025-08-18 08:24:37", 94], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 1079}, {"index": 15, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 828}, {"index": 17, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.096261, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "Cart.php:1079", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 1079}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FCart.php&line=1079", "ajax": false, "filename": "Cart.php", "line": "1079"}, "connection": "mlk", "explain": null, "start_percent": 77.077, "width_percent": 2.088}, {"sql": "update `cart_items` set `tax_percent` = 0, `tax_amount` = 0, `base_tax_amount` = 0, `cart_items`.`updated_at` = '2025-08-18 08:24:37' where `id` = 96", "type": "query", "params": [], "bindings": [0, 0, 0, "2025-08-18 08:24:37", 96], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 1079}, {"index": 15, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 828}, {"index": 17, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.1021569, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "Cart.php:1079", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 1079}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FCart.php&line=1079", "ajax": false, "filename": "Cart.php", "line": "1079"}, "connection": "mlk", "explain": null, "start_percent": 79.165, "width_percent": 2.21}, {"sql": "select * from `cart` where `cart`.`id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 92}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 832}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.104903, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 81.375, "width_percent": 1.792}, {"sql": "select * from `cart_items` where `cart_items`.`cart_id` = 37 and `cart_items`.`cart_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 847}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.107473, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "Cart.php:847", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 847}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FCart.php&line=847", "ajax": false, "filename": "Cart.php", "line": "847"}, "connection": "mlk", "explain": null, "start_percent": 83.167, "width_percent": 3.289}, {"sql": "select * from `cart_shipping_rates` where `cart_shipping_rates`.`cart_id` = 37 and `cart_shipping_rates`.`cart_id` is not null", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 106}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 115}, {"index": 27, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 870}, {"index": 29, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1116002, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Cart.php:106", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCart.php&line=106", "ajax": false, "filename": "Cart.php", "line": "106"}, "connection": "mlk", "explain": null, "start_percent": 86.456, "width_percent": 0.275}, {"sql": "update `cart` set `items_qty` = 4, `grand_total` = 4, `base_grand_total` = 4, `sub_total` = 4, `base_sub_total` = 4, `tax_total` = 0, `base_tax_total` = 0, `discount_amount` = 0, `base_discount_amount` = 0, `shipping_amount` = 0, `base_shipping_amount` = 0, `shipping_amount_incl_tax` = 0, `base_shipping_amount_incl_tax` = 0, `sub_total_incl_tax` = 4, `base_sub_total_incl_tax` = 4, `cart`.`updated_at` = '2025-08-18 08:24:37' where `id` = 37", "type": "query", "params": [], "bindings": [4, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, "2025-08-18 08:24:37", 37], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 902}, {"index": 16, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CartController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CartController.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.112786, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "Cart.php:902", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 902}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FCart.php&line=902", "ajax": false, "filename": "Cart.php", "line": "902"}, "connection": "mlk", "explain": null, "start_percent": 86.731, "width_percent": 2.098}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 37 and `addresses`.`cart_id` is not null and `address_type` = 'cart_billing' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "type": "query", "params": [], "bindings": [37, "cart_billing", "cart_billing", "cart_shipping"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartResource.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.120692, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "mlk", "explain": null, "start_percent": 88.829, "width_percent": 0.255}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 37 and `addresses`.`cart_id` is not null and `address_type` = 'cart_shipping' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "type": "query", "params": [], "bindings": [37, "cart_shipping", "cart_billing", "cart_shipping"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartResource.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.12139, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "mlk", "explain": null, "start_percent": 89.084, "width_percent": 0.173}, {"sql": "select * from `products` where `products`.`id` = 211 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 133}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartResource.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1220171, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Cart.php:133", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCart.php&line=133", "ajax": false, "filename": "Cart.php", "line": "133"}, "connection": "mlk", "explain": null, "start_percent": 89.257, "width_percent": 0.153}, {"sql": "select * from `cart_payment` where `cart_payment`.`cart_id` = 37 and `cart_payment`.`cart_id` is not null limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartResource.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.123066, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "mlk", "explain": null, "start_percent": 89.409, "width_percent": 1.823}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 92 limit 1", "type": "query", "params": [], "bindings": [92], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 535}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 34}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.1268551, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "mlk", "explain": null, "start_percent": 91.232, "width_percent": 0.285}, {"sql": "select * from `products` where `products`.`id` = 213 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 535}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 34}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.127521, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "Configurable.php:535", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 535}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FConfigurable.php&line=535", "ajax": false, "filename": "Configurable.php", "line": "535"}, "connection": "mlk", "explain": null, "start_percent": 91.517, "width_percent": 0.132}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 213 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 535}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 34}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.128138, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 91.65, "width_percent": 1.935}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 35}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.1356108, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 93.585, "width_percent": 1.864}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 35}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.138534, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 95.448, "width_percent": 1.904}, {"sql": "select * from `products` where `products`.`id` = 211 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/CartItem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\CartItem.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 34}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.142265, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "CartItem.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/CartItem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\CartItem.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCartItem.php&line=45", "ajax": false, "filename": "CartItem.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 97.352, "width_percent": 0.193}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 94 limit 1", "type": "query", "params": [], "bindings": [94], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 535}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 34}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.142852, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "mlk", "explain": null, "start_percent": 97.546, "width_percent": 0.326}, {"sql": "select * from `products` where `products`.`id` = 215 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [215], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 535}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 34}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.143533, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Configurable.php:535", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 535}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FConfigurable.php&line=535", "ajax": false, "filename": "Configurable.php", "line": "535"}, "connection": "mlk", "explain": null, "start_percent": 97.872, "width_percent": 0.153}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 215 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [215], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 535}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 34}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.144062, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 98.024, "width_percent": 0.173}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 35}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.144674, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 98.198, "width_percent": 0.153}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 35}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.1458519, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 98.35, "width_percent": 0.255}, {"sql": "select * from `products` where `products`.`id` = 211 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/CartItem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\CartItem.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 34}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.147637, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CartItem.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/CartItem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Checkout\\src\\Models\\CartItem.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCartItem.php&line=45", "ajax": false, "filename": "CartItem.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 98.605, "width_percent": 0.265}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 96 limit 1", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 535}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 34}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.148409, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "mlk", "explain": null, "start_percent": 98.87, "width_percent": 0.234}, {"sql": "select * from `products` where `products`.`id` = 212 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 535}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 34}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.149064, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Configurable.php:535", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 535}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FConfigurable.php&line=535", "ajax": false, "filename": "Configurable.php", "line": "535"}, "connection": "mlk", "explain": null, "start_percent": 99.104, "width_percent": 0.153}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 212 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 535}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 34}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.149637, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 99.257, "width_percent": 0.224}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 35}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.15044, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 99.481, "width_percent": 0.173}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/CartItemResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\CartItemResource.php", "line": 35}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.151453, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 99.654, "width_percent": 0.346}]}, "models": {"data": {"Webkul\\Product\\Models\\ProductAttributeValue": {"value": 237, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 32, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Checkout\\Models\\CartItem": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCartItem.php&line=1", "ajax": false, "filename": "CartItem.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductPriceIndex": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductPriceIndex.php&line=1", "ajax": false, "filename": "ProductPriceIndex.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Checkout\\Models\\Cart": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCart.php&line=1", "ajax": false, "filename": "Cart.php", "line": "?"}}, "Webkul\\CartRule\\Models\\CartRule": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FModels%2FCartRule.php&line=1", "ajax": false, "filename": "CartRule.php", "line": "?"}}, "Webkul\\CartRule\\Models\\CartRuleCoupon": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FModels%2FCartRuleCoupon.php&line=1", "ajax": false, "filename": "CartRuleCoupon.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Tax\\Models\\TaxCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTax%2Fsrc%2FModels%2FTaxCategory.php&line=1", "ajax": false, "filename": "TaxCategory.php", "line": "?"}}}, "count": 336, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/api/checkout/cart", "action_name": "shop.api.checkout.cart.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\CartController@index", "uri": "GET api/checkout/cart", "controller": "Webkul\\Shop\\Http\\Controllers\\API\\CartController@index<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FCartController.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/checkout/cart", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FCartController.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/API/CartController.php:30-43</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance", "duration": "435ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1385568191 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1385568191\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-947534782 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-947534782\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1131464005 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IlBpZDE1SUhGb2ozRGZVWmo1bzU4K1E9PSIsInZhbHVlIjoiZ05uNnhhV0hjVHBqRHp3emFxbDEreUw1ZnZmU08yV3UrY0lEWGJiaDhXV1RlUm10QmxkK1VYTFN2VFFYbkNuM0t4M3p3NmJoa09jYU8zYWtwUExnbkErYXNlcTRaVWhHMHBmMVRiaUlwWG9pakVlZTUwZm1uZE53TWZ1S2t5ZE8iLCJtYWMiOiJlZDU3OWFhMTE5ZDZiNTNjNWExOTFlYjM4ZWNiZWZiNzk5YjkyMDA4MThlNTYwOWI5MGQyMjBiYWJmZGE0M2I2IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6InFjVW9xQ2dzTGUrLzFhY1RDVzh3cGc9PSIsInZhbHVlIjoiV1JBSUQxU0g0NEFrbFRTTmNLSVdoKy9KZlpGSDJhSU5zVzhEdkFWbGFGMXd5ZXY3ZW5VcW56YXkxQnhLblU2clErSENoM09SdmhMNmc2YXYzd3VyU25iaXJEQW9CcHVQK0x4aENFb0tySE1LK1kybnhOLzEwV0I5Z3BPOG80VnoiLCJtYWMiOiJiOWQ5YWFlM2Y4YmZlZGFhMDMzYzMyOWE0YTNmNzdlMWI2M2VkZmVkZWIzYzhjYTIxNGUwMWQzYzM0ZTg5NTY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-C<PERSON>,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://mlk.test/1027-iphone-ip16e?locale=it</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlBpZDE1SUhGb2ozRGZVWmo1bzU4K1E9PSIsInZhbHVlIjoiZ05uNnhhV0hjVHBqRHp3emFxbDEreUw1ZnZmU08yV3UrY0lEWGJiaDhXV1RlUm10QmxkK1VYTFN2VFFYbkNuM0t4M3p3NmJoa09jYU8zYWtwUExnbkErYXNlcTRaVWhHMHBmMVRiaUlwWG9pakVlZTUwZm1uZE53TWZ1S2t5ZE8iLCJtYWMiOiJlZDU3OWFhMTE5ZDZiNTNjNWExOTFlYjM4ZWNiZWZiNzk5YjkyMDA4MThlNTYwOWI5MGQyMjBiYWJmZGE0M2I2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1131464005\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-251198 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ip_address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vnSyIG6iJ55AHBBUPGgOi0i0oU87v90LJuXfNHRl</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">88qQnIrt45zOEcbMyeOCCgAapTdKA6feHMFNH0Bh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251198\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-576377776 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:24:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-576377776\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-970374966 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vnSyIG6iJ55AHBBUPGgOi0i0oU87v90LJuXfNHRl</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">it</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://mlk.test/1027-iphone-ip16e?locale=it</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => {<a class=sf-dump-ref>#1719</a><samp data-depth=2 class=sf-dump-compact>\n    +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">id</span>\": <span class=sf-dump-num>37</span>\n  </samp>}\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-970374966\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/api/checkout/cart", "action_name": "shop.api.checkout.cart.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\CartController@index"}, "badge": null}}