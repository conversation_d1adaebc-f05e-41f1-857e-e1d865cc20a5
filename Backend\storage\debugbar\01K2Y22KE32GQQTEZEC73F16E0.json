{"__meta": {"id": "01K2Y22KE32GQQTEZEC73F16E0", "datetime": "2025-08-18 08:42:35", "utime": **********.972337, "method": "GET", "uri": "/cache/original/product/232/JERAaiglcfWofNqAqeIO8vVLQljrxQ3aX8haHTem.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.8036, "end": **********.981735, "duration": 0.17813491821289062, "duration_str": "178ms", "measures": [{"label": "Booting", "start": **********.8036, "relative_start": 0, "end": **********.954677, "relative_end": **********.954677, "duration": 0.****************, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.954688, "relative_start": 0.*****************, "end": **********.981737, "relative_end": 1.9073486328125e-06, "duration": 0.027048826217651367, "duration_str": "27.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.964514, "relative_start": 0.*****************, "end": **********.967837, "relative_end": **********.967837, "duration": 0.003323078155517578, "duration_str": "3.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.971072, "relative_start": 0.*****************, "end": **********.971179, "relative_end": **********.971179, "duration": 0.00010704994201660156, "duration_str": "107μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.971193, "relative_start": 0.*****************, "end": **********.971206, "relative_end": **********.971206, "duration": 1.2874603271484375e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/232/JERAaiglcfWofNqAqeIO8vVLQljrxQ3aX8haHTem.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "179ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1065259173 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1065259173\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-359767664 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-359767664\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-324167059 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6Im5uT1E2Rng2UUE3QjEzazBhUmNHV0E9PSIsInZhbHVlIjoiVnY3dElpQTVGRmJ5Q0VSUktLMTFYKzR5MmJ6eXNKUVcwUXlkK2NkQUxab3ErTWY1UXJKTml4cVYxTnlpTVRhRm5VOGRsZm4rWEZrSHUvT0JianpldUJvOXBXVzF1bVB2UUJrS29xQXJKZFNyR1liTFpQSlNMcHZVYi9VRG5PTjEiLCJtYWMiOiJiMmU2MzVmNWJjY2VkZDZmMjIzYWNmM2E5MTg1OWVhM2RjNGM2ZTY2ODg3ZDY1ZjI2MDllMTFmMGIxMGY4NTg2IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Ik1HaHZ0UjYya2JvWUFubk1rZkIycWc9PSIsInZhbHVlIjoiRURHNEZFcTM5eVZWakQ2eXlKVFFlVDVQa0tWd3ZmV1RTemxVa25SZlVVLy9UMXk5QlJMZTlCbFZhVWNpUUJHTW9ZLy9QOVdBT3FPWFNzNEdiVUVodUpRakEwbUYzRElrVEFaU1RId1R3bllkOWo3ajJMMVlUN2V2UEdTQ0RTajYiLCJtYWMiOiI0YTE3NThiODEyM2UxZjExMzZkYWUwNjQxYmUzYzkzYWNlZWY2NTg3NzZhNTc5MGYzNjI2M2M5ZTQ2Y2M0MmM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">http://mlk.test/iphone-ip16-metal-bracket-magnetic-suction-shell</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-324167059\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-51047053 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im5uT1E2Rng2UUE3QjEzazBhUmNHV0E9PSIsInZhbHVlIjoiVnY3dElpQTVGRmJ5Q0VSUktLMTFYKzR5MmJ6eXNKUVcwUXlkK2NkQUxab3ErTWY1UXJKTml4cVYxTnlpTVRhRm5VOGRsZm4rWEZrSHUvT0JianpldUJvOXBXVzF1bVB2UUJrS29xQXJKZFNyR1liTFpQSlNMcHZVYi9VRG5PTjEiLCJtYWMiOiJiMmU2MzVmNWJjY2VkZDZmMjIzYWNmM2E5MTg1OWVhM2RjNGM2ZTY2ODg3ZDY1ZjI2MDllMTFmMGIxMGY4NTg2IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik1HaHZ0UjYya2JvWUFubk1rZkIycWc9PSIsInZhbHVlIjoiRURHNEZFcTM5eVZWakQ2eXlKVFFlVDVQa0tWd3ZmV1RTemxVa25SZlVVLy9UMXk5QlJMZTlCbFZhVWNpUUJHTW9ZLy9QOVdBT3FPWFNzNEdiVUVodUpRakEwbUYzRElrVEFaU1RId1R3bllkOWo3ajJMMVlUN2V2UEdTQ0RTajYiLCJtYWMiOiI0YTE3NThiODEyM2UxZjExMzZkYWUwNjQxYmUzYzkzYWNlZWY2NTg3NzZhNTc5MGYzNjI2M2M5ZTQ2Y2M0MmM0IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-51047053\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1416764209 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">279082</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ad81de28123524a046977252794295c5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:42:35 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1416764209\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1607233009 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1607233009\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/232/JERAaiglcfWofNqAqeIO8vVLQljrxQ3aX8haHTem.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}