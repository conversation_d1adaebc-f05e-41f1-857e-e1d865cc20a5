{"__meta": {"id": "01K2Y0ZZYEHVZ0RBW9318XR035", "datetime": "2025-08-18 08:23:41", "utime": **********.903182, "method": "GET", "uri": "/cache/large/product/234/GopiejmMjba5y1UJoy0d4OllASkSyoc6z2LkNuzb.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.732471, "end": **********.911751, "duration": 0.17928004264831543, "duration_str": "179ms", "measures": [{"label": "Booting", "start": **********.732471, "relative_start": 0, "end": **********.884124, "relative_end": **********.884124, "duration": 0.*****************, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.884134, "relative_start": 0.*****************, "end": **********.911753, "relative_end": 1.9073486328125e-06, "duration": 0.027618885040283203, "duration_str": "27.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.894578, "relative_start": 0.*****************, "end": **********.897841, "relative_end": **********.897841, "duration": 0.0032629966735839844, "duration_str": "3.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.901983, "relative_start": 0.*****************, "end": **********.902066, "relative_end": **********.902066, "duration": 8.296966552734375e-05, "duration_str": "83μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.902077, "relative_start": 0.*****************, "end": **********.902088, "relative_end": **********.902088, "duration": 1.0967254638671875e-05, "duration_str": "11μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/product/234/GopiejmMjba5y1UJoy0d4OllASkSyoc6z2LkNuzb.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "180ms", "peak_memory": "36MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-669397086 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-669397086\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-357668420 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-357668420\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1292988178 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IjlFWURuQW04WUJvVFM3ajJJemk0L1E9PSIsInZhbHVlIjoiOHpRM1drZlNiZXdZWjZWOFlXbGZCQ044MVBHYVNZRjFnVHdMbU1UeThLSTY1S3RBLzl0NkduUSt4dERlaUxZclR4S014bW5SOEZLZ3Ewc0p5SHVyNEYrTDNMY1ppWEpLdHhTZnRIbUg0cXdrYTFERGpGSCtETXAyeUhIOStDVjciLCJtYWMiOiIwNTQ1OWU4MTY2YTIzYzVmZjc0MmQyMzE5YmI1MjBlNWUzM2NmNjMyMTdjNjk4YTM5NmU2NGI4MDQ5Y2JlZjNlIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IkM1YVUrNlR1NDBLVm5Zc0NMcEYveVE9PSIsInZhbHVlIjoiN0ZHbmdZQmFpU1A1ak1FdlJPOU1zVXh4YUxuM3BBTGc4anN6eEsxYXBML2ZRTU9XQUZIVzF3b3duZW5EZGpTOG5jSS9rUVVFeFVsSGltMDF3cmZQdzk3QTNPOUd0VzZjZHUrZTFic1QrWWd0YXNEWllUMHI0eGNRVTd2S256Um8iLCJtYWMiOiIyNWI5ZjUzZWQ3NGQ2ODE3NGUxNWQ5YmI2Mzc0OTM5MTUwZjYwNTI2ZDQ1YWRhYmMwZGUxMzQ5MzcwYTc0MTUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://mlk.test/1027-iphone-ip16e</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1292988178\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1568492076 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjlFWURuQW04WUJvVFM3ajJJemk0L1E9PSIsInZhbHVlIjoiOHpRM1drZlNiZXdZWjZWOFlXbGZCQ044MVBHYVNZRjFnVHdMbU1UeThLSTY1S3RBLzl0NkduUSt4dERlaUxZclR4S014bW5SOEZLZ3Ewc0p5SHVyNEYrTDNMY1ppWEpLdHhTZnRIbUg0cXdrYTFERGpGSCtETXAyeUhIOStDVjciLCJtYWMiOiIwNTQ1OWU4MTY2YTIzYzVmZjc0MmQyMzE5YmI1MjBlNWUzM2NmNjMyMTdjNjk4YTM5NmU2NGI4MDQ5Y2JlZjNlIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkM1YVUrNlR1NDBLVm5Zc0NMcEYveVE9PSIsInZhbHVlIjoiN0ZHbmdZQmFpU1A1ak1FdlJPOU1zVXh4YUxuM3BBTGc4anN6eEsxYXBML2ZRTU9XQUZIVzF3b3duZW5EZGpTOG5jSS9rUVVFeFVsSGltMDF3cmZQdzk3QTNPOUd0VzZjZHUrZTFic1QrWWd0YXNEWllUMHI0eGNRVTd2S256Um8iLCJtYWMiOiIyNWI5ZjUzZWQ3NGQ2ODE3NGUxNWQ5YmI2Mzc0OTM5MTUwZjYwNTI2ZDQ1YWRhYmMwZGUxMzQ5MzcwYTc0MTUxIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1568492076\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-863850616 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">16500</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">5bd00e47ecadaca097a9a71071aebccd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:23:41 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-863850616\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1309761237 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1309761237\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/product/234/GopiejmMjba5y1UJoy0d4OllASkSyoc6z2LkNuzb.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}