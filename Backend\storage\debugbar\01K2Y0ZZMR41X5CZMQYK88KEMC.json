{"__meta": {"id": "01K2Y0ZZMR41X5CZMQYK88KEMC", "datetime": "2025-08-18 08:23:41", "utime": **********.593267, "method": "GET", "uri": "/cache/original/product/234/Me5uggV0i9RGJDV3jSTGUIW3ZquklINZbmGzGiN1.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.40862, "end": **********.603079, "duration": 0.1944589614868164, "duration_str": "194ms", "measures": [{"label": "Booting", "start": **********.40862, "relative_start": 0, "end": **********.576175, "relative_end": **********.576175, "duration": 0.****************, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.576185, "relative_start": 0.*****************, "end": **********.603081, "relative_end": 1.9073486328125e-06, "duration": 0.026895999908447266, "duration_str": "26.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.585792, "relative_start": 0.*****************, "end": **********.589184, "relative_end": **********.589184, "duration": 0.0033919811248779297, "duration_str": "3.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.591921, "relative_start": 0.*****************, "end": **********.592017, "relative_end": **********.592017, "duration": 9.584426879882812e-05, "duration_str": "96μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.592029, "relative_start": 0.****************, "end": **********.592041, "relative_end": **********.592041, "duration": 1.1920928955078125e-05, "duration_str": "12μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/234/Me5uggV0i9RGJDV3jSTGUIW3ZquklINZbmGzGiN1.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "195ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2117727509 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2117727509\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2077388672 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2077388672\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2146455708 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IjBqU1dNNmV0a1FGaW1pZG1yNzlyNkE9PSIsInZhbHVlIjoiSVJDdjNUSGpPSG1jdmt5WFQ1RnZtdkhDUEIyOTJjSDBCc0xLT2ZraUhuQ3lJdUtIMXowWnF2VWRJbWFBaHV2TmRwaEZaNzJ6WEtTTVhob0R6YXZZVXdkSndMUDdFNjA5VG1BVnAvWFJqNGljSlREek5UMTF0Y3NNazY3RC9DNm4iLCJtYWMiOiI0MGQ2N2IxMzUwNGZlNWQ2NjFiY2FhZWMxOTllMDUwZmFjMWIxNzYwOTg3NzM2NmExOGMzYjE2OWVmYWY1NDIwIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IjJxamFad29SVzMyTzdJYW9UYzhQaUE9PSIsInZhbHVlIjoiV0dXTGRsd2tvWHBEbWpHNnBzWlorVVJNWVhpMmh4eHJWSm5BUzZHbWRTYkg2NjF0Y0xUMGpwZ05iUWhoZ2dKUit3NFc4RmNpRWVIZFZtREtKOWJBTmJkZEhKQzJ0ZnY1bDNpQkJVY0duTmp6dXppelQrUDN2L1ozQkd2L1NyNmEiLCJtYWMiOiJiZThiOTJiZDljMTIwZTMxNWU4YTc4MGRjMjRjMzAzZDlkNTgxMmQzNmUzNzA1NzhmMGU4ZjI3ZDNmZGY3Y2QzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-C<PERSON>,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://mlk.test/1027-iphone-ip16e</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146455708\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1606443253 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjBqU1dNNmV0a1FGaW1pZG1yNzlyNkE9PSIsInZhbHVlIjoiSVJDdjNUSGpPSG1jdmt5WFQ1RnZtdkhDUEIyOTJjSDBCc0xLT2ZraUhuQ3lJdUtIMXowWnF2VWRJbWFBaHV2TmRwaEZaNzJ6WEtTTVhob0R6YXZZVXdkSndMUDdFNjA5VG1BVnAvWFJqNGljSlREek5UMTF0Y3NNazY3RC9DNm4iLCJtYWMiOiI0MGQ2N2IxMzUwNGZlNWQ2NjFiY2FhZWMxOTllMDUwZmFjMWIxNzYwOTg3NzM2NmExOGMzYjE2OWVmYWY1NDIwIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjJxamFad29SVzMyTzdJYW9UYzhQaUE9PSIsInZhbHVlIjoiV0dXTGRsd2tvWHBEbWpHNnBzWlorVVJNWVhpMmh4eHJWSm5BUzZHbWRTYkg2NjF0Y0xUMGpwZ05iUWhoZ2dKUit3NFc4RmNpRWVIZFZtREtKOWJBTmJkZEhKQzJ0ZnY1bDNpQkJVY0duTmp6dXppelQrUDN2L1ozQkd2L1NyNmEiLCJtYWMiOiJiZThiOTJiZDljMTIwZTMxNWU4YTc4MGRjMjRjMzAzZDlkNTgxMmQzNmUzNzA1NzhmMGU4ZjI3ZDNmZGY3Y2QzIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1606443253\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1076576989 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">40600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">af87a89019573daae656ed84f9d60ae5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:23:41 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076576989\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1023310564 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1023310564\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/234/Me5uggV0i9RGJDV3jSTGUIW3ZquklINZbmGzGiN1.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}