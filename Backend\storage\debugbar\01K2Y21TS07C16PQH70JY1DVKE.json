{"__meta": {"id": "01K2Y21TS07C16PQH70JY1DVKE", "datetime": "2025-08-18 08:42:10", "utime": **********.720504, "method": "GET", "uri": "/api/products/view-data/iphone-ip1314", "ip": "127.0.0.1"}, "modules": {"count": 5, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (39)", "Webkul\\Attribute\\Models\\AttributeFamily (5)", "Webkul\\Attribute\\Models\\AttributeTranslation (35)", "Webkul\\Attribute\\Models\\AttributeOption (12)", "Webkul\\Attribute\\Models\\AttributeOptionTranslation (65)"], "views": [], "queries": [{"sql": "select * from `attributes` where `code` = 'url_key'", "duration": 1.68, "duration_str": "1.68s", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "duration": 2.88, "duration_str": "2.88s", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = 244", "duration": 1.76, "duration_str": "1.76s", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` in (23)", "duration": 1.64, "duration_str": "1.64s", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (23)", "duration": 1.51, "duration_str": "1.51s", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` in (1, 2, 3, 4, 5, 495, 496, 497, 498)", "duration": 1.58, "duration_str": "1.58s", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2 and `attributes`.`is_visible_on_front` = 1", "duration": 1.77, "duration_str": "1.77s", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 23 and `attribute_translations`.`attribute_id` is not null", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`id` = 371 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 371 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 25 and `attribute_translations`.`attribute_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `id` in ('392', '394')", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 392 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 394 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 33 and `attribute_translations`.`attribute_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 38 and `attribute_translations`.`attribute_id` is not null", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 39 and `attribute_translations`.`attribute_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (6)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 1.16, "duration_str": "1.16s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 3.23, "duration_str": "3.23s", "connection": "mlk"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 2.81, "duration_str": "2.81s", "connection": "mlk"}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 2", "duration": 1.38, "duration_str": "1.38s", "connection": "mlk"}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "duration": 1.55, "duration_str": "1.55s", "connection": "mlk"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (1)"], "views": [], "queries": [{"sql": "select * from `customer_groups` where `code` = 'guest'", "duration": 1.66, "duration_str": "1.66s", "connection": "mlk"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\ProductAttributeValue (83)", "Webkul\\Product\\Models\\Product (8)", "Webkul\\Product\\Models\\ProductImage (9)", "Webkul\\Product\\Models\\ProductPriceIndex (12)", "Webkul\\Product\\Models\\ProductInventoryIndex (4)"], "views": [], "queries": [{"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = 'iphone-ip1314'", "duration": 0.68, "duration_str": "680ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 244 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 244 and `product_attribute_values`.`product_id` is not null", "duration": 0.31, "duration_str": "310ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 244 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` = 244 and `product_price_indices`.`product_id` is not null", "duration": 1.42, "duration_str": "1.42s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 244 and `products`.`parent_id` is not null", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 245 and `product_attribute_values`.`product_id` is not null", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 245 and `product_inventory_indices`.`product_id` is not null", "duration": 1.45, "duration_str": "1.45s", "connection": "mlk"}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` = 244 and `product_reviews`.`product_id` is not null", "duration": 1.61, "duration_str": "1.61s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 244 and `products`.`parent_id` is not null", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` in (244)", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (245, 246, 247)", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (245, 246, 247)", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (245, 246, 247)", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (245, 246, 247) order by `position` asc", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (245, 246, 247) order by `position` asc", "duration": 1.55, "duration_str": "1.55s", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": [], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.11, "duration_str": "110ms", "connection": "mlk"}]}]}, "messages": {"count": 3, "messages": [{"message": "[08:42:10] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.617621, "xdebug_link": null, "collector": "log"}, {"message": "[08:42:10] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.652959, "xdebug_link": null, "collector": "log"}, {"message": "[08:42:10] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.710717, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.4198, "end": **********.727122, "duration": 0.30732202529907227, "duration_str": "307ms", "measures": [{"label": "Booting", "start": **********.4198, "relative_start": 0, "end": **********.569112, "relative_end": **********.569112, "duration": 0.*****************, "duration_str": "149ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.569122, "relative_start": 0.****************, "end": **********.727124, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "158ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.578032, "relative_start": 0.****************, "end": **********.580692, "relative_end": **********.580692, "duration": 0.002660036087036133, "duration_str": "2.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::products.prices.configurable", "start": **********.650063, "relative_start": 0.*****************, "end": **********.650063, "relative_end": **********.650063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.717363, "relative_start": 0.****************, "end": **********.719455, "relative_end": **********.719455, "duration": 0.0020918846130371094, "duration_str": "2.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "shop::products.prices.configurable", "param_count": null, "params": [], "start": **********.650048, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/products/prices/configurable.blade.phpshop::products.prices.configurable", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fconfigurable.blade.php&line=1", "ajax": false, "filename": "configurable.blade.php", "line": "?"}}]}, "queries": {"count": 48, "nb_statements": 48, "nb_visible_statements": 48, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03694000000000001, "accumulated_duration_str": "36.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "theme", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.594162, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 3.14}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.598249, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 3.14, "width_percent": 8.744}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.602805, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 11.884, "width_percent": 7.607}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.609418, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 19.491, "width_percent": 0.596}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.609991, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 20.087, "width_percent": 0.325}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.610476, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 20.411, "width_percent": 0.46}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.610965, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 20.872, "width_percent": 0.298}, {"sql": "select * from `attributes` where `code` = 'url_key'", "type": "query", "params": [], "bindings": ["url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 125}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}], "start": **********.6117392, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 21.169, "width_percent": 4.548}, {"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = 'iphone-ip1314'", "type": "query", "params": [], "bindings": [3, "iphone-ip1314"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 191}], "start": **********.614053, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 25.717, "width_percent": 1.841}, {"sql": "select * from `products` where `products`.`id` = 244 limit 1", "type": "query", "params": [], "bindings": [244], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 161}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 191}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 175}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6156461, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:161", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductRepository.php&line=161", "ajax": false, "filename": "ProductRepository.php", "line": "161"}, "connection": "mlk", "explain": null, "start_percent": 27.558, "width_percent": 0.379}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 26, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 36}], "start": **********.619723, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 27.937, "width_percent": 0.541}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 190}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}], "start": **********.6205049, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mlk", "explain": null, "start_percent": 28.479, "width_percent": 7.796}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 244 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [244], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 36}], "start": **********.6253371, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 36.275, "width_percent": 0.839}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 244 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [244], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/ProductImage.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\ProductImage.php", "line": 109}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/ProductImage.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\ProductImage.php", "line": 98}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 39}], "start": **********.628222, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 37.114, "width_percent": 0.541}, {"sql": "select * from `customer_groups` where `code` = 'guest'", "type": "query", "params": [], "bindings": ["guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 746}, {"index": 19, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}], "start": **********.6360228, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 37.656, "width_percent": 4.494}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` = 244 and `product_price_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [244], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 684}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 715}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 43}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 179}], "start": **********.638333, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 42.149, "width_percent": 3.844}, {"sql": "select * from `products` where `products`.`parent_id` = 244 and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [244], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 608}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 179}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.640256, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 45.994, "width_percent": 0.65}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 140}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.642563, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 46.643, "width_percent": 0.433}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 245 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [245], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 140}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.643534, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 47.076, "width_percent": 0.514}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 245 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [245], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 700}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 507}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 156}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 144}], "start": **********.64485, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 47.591, "width_percent": 3.925}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 457}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 479}], "start": **********.647295, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 51.516, "width_percent": 3.736}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` = 244 and `product_reviews`.`product_id` is not null", "type": "query", "params": [], "bindings": [244], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Review.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Review.php", "line": 28}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 52}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 179}], "start": **********.6509008, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 55.252, "width_percent": 4.358}, {"sql": "select * from `products` where `products`.`parent_id` = 244 and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [244], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 18, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 182}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.653322, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 59.61, "width_percent": 0.596}, {"sql": "select * from `products` where `products`.`id` in (244)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 182}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.6540852, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 60.206, "width_percent": 0.352}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (245, 246, 247)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 182}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.654629, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 60.558, "width_percent": 0.677}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (245, 246, 247)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 182}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.655483, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 61.234, "width_percent": 0.46}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (245, 246, 247)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 182}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.656167, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 61.695, "width_percent": 0.812}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (245, 246, 247) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 182}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.657096, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 62.507, "width_percent": 0.623}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (245, 246, 247) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 182}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.657857, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=45", "ajax": false, "filename": "ConfigurableOption.php", "line": "45"}, "connection": "mlk", "explain": null, "start_percent": 63.129, "width_percent": 4.196}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 140}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.660485, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 67.325, "width_percent": 0.379}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 140}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.662681, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 67.704, "width_percent": 0.352}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 140}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.664675, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 68.056, "width_percent": 0.379}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = 244", "type": "query", "params": [], "bindings": [244], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 105}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 18, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 182}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.666356, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:91", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=91", "ajax": false, "filename": "ConfigurableOption.php", "line": "91"}, "connection": "mlk", "explain": null, "start_percent": 68.435, "width_percent": 4.764}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 105}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 182}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.668722, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:91", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=91", "ajax": false, "filename": "ConfigurableOption.php", "line": "91"}, "connection": "mlk", "explain": null, "start_percent": 73.2, "width_percent": 4.44}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 105}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 182}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6709611, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:91", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=91", "ajax": false, "filename": "ConfigurableOption.php", "line": "91"}, "connection": "mlk", "explain": null, "start_percent": 77.639, "width_percent": 4.088}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` in (1, 2, 3, 4, 5, 495, 496, 497, 498)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 105}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 64}, {"index": 28, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 182}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.673056, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "ConfigurableOption.php:91", "source": {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/ConfigurableOption.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\ConfigurableOption.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FConfigurableOption.php&line=91", "ajax": false, "filename": "ConfigurableOption.php", "line": "91"}, "connection": "mlk", "explain": null, "start_percent": 81.727, "width_percent": 4.277}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2 and `attributes`.`is_visible_on_front` = 1", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 19}, {"index": 16, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 186}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.689363, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "View.php:19", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FView.php&line=19", "ajax": false, "filename": "View.php", "line": "19"}, "connection": "mlk", "explain": null, "start_percent": 86.004, "width_percent": 4.792}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 23 and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 60}], "start": **********.693044, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 90.796, "width_percent": 0.568}, {"sql": "select * from `attribute_options` where `attribute_options`.`id` = 371 limit 1", "type": "query", "params": [], "bindings": [371], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 30}, {"index": 21, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 186}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.695198, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 91.364, "width_percent": 0.406}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 371 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [371], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 33}], "start": **********.696308, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 91.77, "width_percent": 0.487}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 25 and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 60}], "start": **********.6979501, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 92.258, "width_percent": 0.46}, {"sql": "select * from `attribute_options` where `id` in ('392', '394')", "type": "query", "params": [], "bindings": ["392", "394"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 45}, {"index": 17, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.69969, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 92.718, "width_percent": 0.46}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 392 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [392], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 48}], "start": **********.700846, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 93.178, "width_percent": 0.596}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 394 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [394], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 48}], "start": **********.702862, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 93.774, "width_percent": 0.623}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 33 and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 60}], "start": **********.704913, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 94.396, "width_percent": 0.487}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 38 and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 60}], "start": **********.707306, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 94.884, "width_percent": 0.433}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 39 and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 60}], "start": **********.709583, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 95.317, "width_percent": 0.487}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 215}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 470}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 26, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 211}], "start": **********.713608, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 95.804, "width_percent": 4.196}]}, "models": {"data": {"Webkul\\Product\\Models\\ProductAttributeValue": {"value": 83, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeOptionTranslation": {"value": 65, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeOptionTranslation.php&line=1", "ajax": false, "filename": "AttributeOptionTranslation.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 39, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeTranslation": {"value": 35, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeTranslation.php&line=1", "ajax": false, "filename": "AttributeTranslation.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductPriceIndex": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductPriceIndex.php&line=1", "ajax": false, "filename": "ProductPriceIndex.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeOption": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeOption.php&line=1", "ajax": false, "filename": "AttributeOption.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventoryIndex": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventoryIndex.php&line=1", "ajax": false, "filename": "ProductInventoryIndex.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}}, "count": 281, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/api/products/view-data/iphone-ip1314", "action_name": "shop.api.products.view_data", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@viewData", "uri": "GET api/products/view-data/{urlKey}", "controller": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@viewData<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=173\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/products", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=173\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php:173-249</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance", "duration": "310ms", "peak_memory": "42MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1880564940 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1880564940\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-311277137 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-311277137\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2024328314 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"783 characters\">sidebar_collapsed=0; dark_mode=0; cookie-consent=1; ip_address=127.0.0.1; XSRF-TOKEN=eyJpdiI6IkRTMnhJRGo0OUhvMDdLSlA1Uk5jMnc9PSIsInZhbHVlIjoiN1NNMUdwU0ZHUlZ3WHRVaThIVDVmbFBUL3o0cDVwM1NLSVpmU01zWDA1RkZmVDNlMko2L3EwTVRlYUkzdk1XQndnTEZFYjNEZ1ZFdWhOdisycUdkY0FjeDZrc3VvRWcwL2I2NHF0WG5UQmdReCs1UVNka0FKS2ZkZ3l6U3gycVoiLCJtYWMiOiI1NzI0MmNkMDRjOTk2ODU5OTJmMDM1MWI3NWFhZTg5Y2E0YzZiNTA5ZmNhM2FhYWFjMjJjMTdkZTFhOGJhMGJiIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IlNqemRyREFObjhINTJ4cEJCa01zY1E9PSIsInZhbHVlIjoidndzbnVyVjRvVVVRMTMvNzNUYlU5MTh4T0JUcUZjL0VObWZ3WFpiejMxY2t0U2NxQmNhSFBiYTBHZkJLNm41ZGhNU1p0U1N2UGJrMjhRZStBUHJPQVlBTzNURVArVDJWYndaZEJYaGRMQ20xNGdrMDhhMmlFVnBSQ0JQMnFINlciLCJtYWMiOiI1OWU2NTMzZWFmYWQ2ODMxN2JkZGIyMzA4ZDJmY2E5ZTM3MzQwYTI2ZGE1OWNmZDZmNDJjYjAzNDdmNDYxMGVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://mlk.test/iphone-ip1314</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkRTMnhJRGo0OUhvMDdLSlA1Uk5jMnc9PSIsInZhbHVlIjoiN1NNMUdwU0ZHUlZ3WHRVaThIVDVmbFBUL3o0cDVwM1NLSVpmU01zWDA1RkZmVDNlMko2L3EwTVRlYUkzdk1XQndnTEZFYjNEZ1ZFdWhOdisycUdkY0FjeDZrc3VvRWcwL2I2NHF0WG5UQmdReCs1UVNka0FKS2ZkZ3l6U3gycVoiLCJtYWMiOiI1NzI0MmNkMDRjOTk2ODU5OTJmMDM1MWI3NWFhZTg5Y2E0YzZiNTA5ZmNhM2FhYWFjMjJjMTdkZTFhOGJhMGJiIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024328314\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-570812311 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>cookie-consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ip_address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vnSyIG6iJ55AHBBUPGgOi0i0oU87v90LJuXfNHRl</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">88qQnIrt45zOEcbMyeOCCgAapTdKA6feHMFNH0Bh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-570812311\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1211663905 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 07:42:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1211663905\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1623785492 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vnSyIG6iJ55AHBBUPGgOi0i0oU87v90LJuXfNHRl</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://mlk.test/iphone-ip1212-pro</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => {<a class=sf-dump-ref>#1713</a><samp data-depth=2 class=sf-dump-compact>\n    +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">id</span>\": <span class=sf-dump-num>37</span>\n  </samp>}\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623785492\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/api/products/view-data/iphone-ip1314", "action_name": "shop.api.products.view_data", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@viewData"}, "badge": null}}